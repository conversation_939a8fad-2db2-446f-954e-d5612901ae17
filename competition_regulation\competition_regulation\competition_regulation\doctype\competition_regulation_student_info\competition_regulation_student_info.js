// Copyright (c) 2025, 1 and contributors
// For license information, please see license.txt

frappe.ui.form.on('Competition Regulation Student Info', {
	// 表单刷新时触发
	refresh: function(frm) {
		// 设置字段描述
		frm.set_df_property('student_id', 'description', '请输入8-20位数字或数字字母组合的学号');
		frm.set_df_property('student_name', 'description', '请输入学生的真实姓名');
		frm.set_df_property('email', 'description', '请输入有效的电子邮件地址');
		frm.set_df_property('mobile_phone', 'description', '请输入有效的中国大陆手机号码');
		frm.set_df_property('college', 'description', '请输入学院的完整名称');
		frm.set_df_property('major', 'description', '请输入专业的完整名称');
		frm.set_df_property('class_name', 'description', '请输入行政班名称');
		
		// 如果是新文档，设置焦点到学号字段
		if (frm.is_new()) {
			frm.set_focus('student_id');
		}
		
		// 设置字段权限（学生只能修改特定字段）
		set_field_permissions(frm);
		
		// 添加自定义按钮
		if (!frm.is_new()) {
			frm.add_custom_button('查看参赛记录', function() {
				// 调用服务器端方法获取参赛记录
				frappe.call({
					method: 'get_student_competitions',
					doc: frm.doc,
					callback: function(r) {
						if (r.message && r.message.length > 0) {
							// 显示参赛记录
							frappe.msgprint({
								title: '参赛记录',
								message: '该学生的参赛记录：' + r.message.join(', ')
							});
						} else {
							frappe.msgprint('该学生暂无参赛记录');
						}
					}
				});
			});
		}
	},
	
	// 学号字段变化时触发
	student_id: function(frm) {
		// 自动去除首尾空格并转为大写
		if (frm.doc.student_id) {
			frm.set_value('student_id', frm.doc.student_id.trim().toUpperCase());
		}
	},
	
	// 学生姓名字段变化时触发
	student_name: function(frm) {
		// 自动去除首尾空格
		if (frm.doc.student_name) {
			frm.set_value('student_name', frm.doc.student_name.trim());
		}
	},
	
	// 电子邮件字段变化时触发
	email: function(frm) {
		// 自动去除首尾空格并转为小写
		if (frm.doc.email) {
			frm.set_value('email', frm.doc.email.trim().toLowerCase());
		}
	},
	
	// 手机号码字段变化时触发
	mobile_phone: function(frm) {
		// 自动去除首尾空格
		if (frm.doc.mobile_phone) {
			frm.set_value('mobile_phone', frm.doc.mobile_phone.trim());
		}
	},
	
	// 学院字段变化时触发
	college: function(frm) {
		// 自动去除首尾空格
		if (frm.doc.college) {
			frm.set_value('college', frm.doc.college.trim());
		}
	},
	
	// 专业字段变化时触发
	major: function(frm) {
		// 自动去除首尾空格
		if (frm.doc.major) {
			frm.set_value('major', frm.doc.major.trim());
		}
	},
	
	// 行政班字段变化时触发
	class_name: function(frm) {
		// 自动去除首尾空格
		if (frm.doc.class_name) {
			frm.set_value('class_name', frm.doc.class_name.trim());
		}
	},
	
	// 表单验证
	validate: function(frm) {
		// 验证学号
		if (!frm.doc.student_id || frm.doc.student_id.trim() === '') {
			frappe.msgprint({
				title: '验证错误',
				indicator: 'red',
				message: '学号不能为空'
			});
			frappe.validated = false;
			return false;
		}
		
		// 验证学号格式
		var studentIdPattern = /^[A-Za-z0-9]{8,20}$/;
		if (!studentIdPattern.test(frm.doc.student_id)) {
			frappe.msgprint({
				title: '验证错误',
				indicator: 'red',
				message: '学号格式不正确，应为8-20位数字或数字字母组合'
			});
			frappe.validated = false;
			return false;
		}
		
		// 验证学生姓名
		if (!frm.doc.student_name || frm.doc.student_name.trim() === '') {
			frappe.msgprint({
				title: '验证错误',
				indicator: 'red',
				message: '学生姓名不能为空'
			});
			frappe.validated = false;
			return false;
		}
		
		// 验证姓名长度
		if (frm.doc.student_name.length < 2 || frm.doc.student_name.length > 10) {
			frappe.msgprint({
				title: '验证错误',
				indicator: 'red',
				message: '学生姓名长度应在2-10个字符之间'
			});
			frappe.validated = false;
			return false;
		}
		
		// 验证性别
		if (!frm.doc.gender) {
			frappe.msgprint({
				title: '验证错误',
				indicator: 'red',
				message: '请选择性别'
			});
			frappe.validated = false;
			return false;
		}
		
		// 验证电子邮件
		if (!frm.doc.email || frm.doc.email.trim() === '') {
			frappe.msgprint({
				title: '验证错误',
				indicator: 'red',
				message: '电子邮件地址不能为空'
			});
			frappe.validated = false;
			return false;
		}
		
		// 验证邮箱格式
		var emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
		if (!emailPattern.test(frm.doc.email)) {
			frappe.msgprint({
				title: '验证错误',
				indicator: 'red',
				message: '电子邮件地址格式不正确'
			});
			frappe.validated = false;
			return false;
		}
		
		// 验证手机号码
		if (!frm.doc.mobile_phone || frm.doc.mobile_phone.trim() === '') {
			frappe.msgprint({
				title: '验证错误',
				indicator: 'red',
				message: '手机号码不能为空'
			});
			frappe.validated = false;
			return false;
		}
		
		// 验证手机号码格式
		var mobilePattern = /^1[3-9]\d{9}$/;
		if (!mobilePattern.test(frm.doc.mobile_phone)) {
			frappe.msgprint({
				title: '验证错误',
				indicator: 'red',
				message: '手机号码格式不正确，请输入有效的中国大陆手机号码'
			});
			frappe.validated = false;
			return false;
		}
		
		// 验证学院
		if (!frm.doc.college || frm.doc.college.trim() === '') {
			frappe.msgprint({
				title: '验证错误',
				indicator: 'red',
				message: '学院不能为空'
			});
			frappe.validated = false;
			return false;
		}
		
		// 验证专业
		if (!frm.doc.major || frm.doc.major.trim() === '') {
			frappe.msgprint({
				title: '验证错误',
				indicator: 'red',
				message: '专业不能为空'
			});
			frappe.validated = false;
			return false;
		}
		
		// 验证行政班
		if (!frm.doc.class_name || frm.doc.class_name.trim() === '') {
			frappe.msgprint({
				title: '验证错误',
				indicator: 'red',
				message: '行政班不能为空'
			});
			frappe.validated = false;
			return false;
		}
	}
});

// 设置字段权限的函数
function set_field_permissions(frm) {
	// 获取当前用户角色
	var user_roles = frappe.user_roles;
	
	// 如果是学生角色且不是教务处教师或竞赛负责教师
	if (user_roles.includes('学生') && 
		!user_roles.includes('教务处教师') && 
		!user_roles.includes('竞赛负责教师')) {
		
		// 如果不是新文档，设置只读字段
		if (!frm.is_new()) {
			// 学生创建后不能修改的字段
			frm.set_df_property('student_id', 'read_only', 1);
			frm.set_df_property('student_name', 'read_only', 1);
			frm.set_df_property('gender', 'read_only', 1);
			frm.set_df_property('email', 'read_only', 1);
			
			// 学生可以修改的字段保持可编辑
			frm.set_df_property('college', 'read_only', 0);
			frm.set_df_property('major', 'read_only', 0);
			frm.set_df_property('class_name', 'read_only', 0);
			frm.set_df_property('mobile_phone', 'read_only', 0);
		}
	}
}
