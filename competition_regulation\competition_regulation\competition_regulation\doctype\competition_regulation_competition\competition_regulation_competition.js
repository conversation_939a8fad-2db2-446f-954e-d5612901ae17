// Copyright (c) 2025, 1 and contributors
// For license information, please see license.txt

frappe.ui.form.on('Competition Regulation Competition', {
	// 表单刷新时触发
	refresh: function(frm) {
		// 设置字段描述
		frm.set_df_property('competition_title', 'description', '请输入具体赛事标题，如"2024年春季ACM程序设计竞赛"');
		frm.set_df_property('competition_category', 'description', '请先选择赛事种类');
		frm.set_df_property('project_name', 'description', '请从赛事种类的项目列表中选择');

		// 如果是新文档，设置焦点到赛事标题字段
		if (frm.is_new()) {
			frm.set_focus('competition_title');
		}

		// 如果已选择赛事种类，加载项目选项
		if (frm.doc.competition_category) {
			load_project_options(frm);
		}

		// 添加自定义按钮
		if (!frm.is_new()) {
			add_custom_buttons(frm);
		}

		// 设置状态字段的颜色
		set_status_color(frm);
	},
	
	// 赛事标题字段变化时触发
	competition_title: function(frm) {
		// 自动去除首尾空格
		if (frm.doc.competition_title) {
			frm.set_value('competition_title', frm.doc.competition_title.trim());
		}
	},
	
	// 赛事种类字段变化时触发
	competition_category: function(frm) {
		if (frm.doc.competition_category) {
			// 清空项目名称
			frm.set_value('project_name', '');

			// 加载项目选项
			load_project_options(frm);
		} else {
			// 清空项目选项
			frm.set_df_property('project_name', 'options', '');
			frm.set_value('project_name', '');
		}
	},

	// 项目名称字段变化时触发
	project_name: function(frm) {
		// 可以在这里添加项目选择后的逻辑
		if (frm.doc.project_name) {
			// 例如：根据项目类型设置默认的参赛形式
			// 这里可以扩展更多逻辑
		}
	},
	
	// 参赛形式字段变化时触发
	participation_type: function(frm) {
		// 根据参赛形式显示/隐藏团队人数限制字段
		if (frm.doc.participation_type === '团队赛') {
			frm.set_df_property('team_size_limit', 'reqd', 1);
			frm.set_df_property('team_size_limit', 'hidden', 0);
			
			// 如果没有设置团队人数限制，设置默认值
			if (!frm.doc.team_size_limit) {
				frm.set_value('team_size_limit', 3);
			}
		} else {
			frm.set_df_property('team_size_limit', 'reqd', 0);
			frm.set_df_property('team_size_limit', 'hidden', 1);
			frm.set_value('team_size_limit', null);
		}
	},
	
	// 报名开始时间字段变化时触发
	registration_start_datetime: function(frm) {
		validate_datetime_sequence(frm);
	},
	
	// 报名结束时间字段变化时触发
	registration_end_datetime: function(frm) {
		validate_datetime_sequence(frm);
	},
	
	// 比赛开始时间字段变化时触发
	competition_start_datetime: function(frm) {
		validate_datetime_sequence(frm);
	},
	
	// 比赛结束时间字段变化时触发
	competition_end_datetime: function(frm) {
		validate_datetime_sequence(frm);
	}
});

// 指导教师子表事件
frappe.ui.form.on('Competition Regulation Competition Instructor Assignment', {
	// 指导教师字段变化时触发
	instructor: function(frm, cdt, cdn) {
		var row = locals[cdt][cdn];
		if (row.instructor) {
			// 自动获取教师姓名
			frappe.db.get_value('Competition Regulation Competition Instructor', row.instructor, 'instructor_name')
				.then(r => {
					if (r.message) {
						frappe.model.set_value(cdt, cdn, 'instructor_name', r.message.instructor_name);
					}
				});
		}
	}
});

// 添加自定义按钮
function add_custom_buttons(frm) {
	// 刷新状态按钮
	frm.add_custom_button('刷新状态', function() {
		frappe.call({
			method: 'refresh_status',
			doc: frm.doc,
			callback: function(r) {
				frm.reload_doc();
			}
		});
	});
	
	// 获取赛事摘要按钮
	frm.add_custom_button('赛事摘要', function() {
		frappe.call({
			method: 'get_competition_summary',
			doc: frm.doc,
			callback: function(r) {
				if (r.message) {
					show_competition_summary(r.message);
				}
			}
		});
	});
	
	// 查看报名情况按钮
	frm.add_custom_button('查看报名', function() {
		frappe.route_options = {
			"competition": frm.doc.name
		};
		frappe.set_route("List", "Competition Regulation Registration");
	});
}

// 设置状态字段的颜色
function set_status_color(frm) {
	if (frm.doc.status) {
		var color_map = {
			'报名未开始': 'gray',
			'报名中': 'green',
			'报名结束': 'orange',
			'比赛中': 'blue',
			'比赛结束': 'red'
		};
		
		var color = color_map[frm.doc.status] || 'gray';
		frm.set_df_property('status', 'color', color);
	}
}

// 验证时间序列
function validate_datetime_sequence(frm) {
	var doc = frm.doc;
	
	// 检查所有时间字段是否都已填写
	if (!doc.registration_start_datetime || !doc.registration_end_datetime || 
		!doc.competition_start_datetime || !doc.competition_end_datetime) {
		return;
	}
	
	var reg_start = new Date(doc.registration_start_datetime);
	var reg_end = new Date(doc.registration_end_datetime);
	var comp_start = new Date(doc.competition_start_datetime);
	var comp_end = new Date(doc.competition_end_datetime);
	
	// 验证时间序列
	if (reg_start >= reg_end) {
		frappe.msgprint('报名开始时间必须早于报名结束时间');
		return false;
	}
	
	if (reg_end > comp_start) {
		frappe.msgprint('报名结束时间不能晚于比赛开始时间');
		return false;
	}
	
	if (comp_start >= comp_end) {
		frappe.msgprint('比赛开始时间必须早于比赛结束时间');
		return false;
	}
	
	return true;
}

// 显示赛事摘要
function show_competition_summary(summary) {
	var html = `
		<div class="competition-summary">
			<h4>赛事摘要</h4>
			<table class="table table-bordered">
				<tr><td><strong>赛事标题</strong></td><td>${summary.competition_title}</td></tr>
				<tr><td><strong>赛事种类</strong></td><td>${summary.competition_category}</td></tr>
				<tr><td><strong>项目名称</strong></td><td>${summary.project_name}</td></tr>
				<tr><td><strong>参赛形式</strong></td><td>${summary.participation_type}</td></tr>
				${summary.team_size_limit ? `<tr><td><strong>团队人数限制</strong></td><td>${summary.team_size_limit}人</td></tr>` : ''}
				<tr><td><strong>当前状态</strong></td><td>${summary.status}</td></tr>
				<tr><td><strong>报名时间</strong></td><td>${summary.registration_period.start} 至 ${summary.registration_period.end}</td></tr>
				<tr><td><strong>比赛时间</strong></td><td>${summary.competition_period.start} 至 ${summary.competition_period.end}</td></tr>
				<tr><td><strong>指导教师数量</strong></td><td>${summary.instructor_count}人</td></tr>
			</table>
		</div>
	`;
	
	frappe.msgprint({
		title: '赛事摘要',
		message: html,
		wide: true
	});
}

// 加载项目选项
function load_project_options(frm) {
	if (frm.doc.competition_category) {
		frappe.call({
			method: 'get_available_projects',
			doc: frm.doc,
			callback: function(r) {
				if (r.message && r.message.length > 0) {
					// 设置项目名称字段的选项
					frm.set_df_property('project_name', 'options', r.message.join('\n'));
				} else {
					frappe.msgprint('该赛事种类没有可用的项目');
					frm.set_df_property('project_name', 'options', '');
				}
			}
		});
	}
}
