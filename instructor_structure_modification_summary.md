# 指导教师结构修改总结

## 🎯 修改需求

根据用户要求进行以下修改：

1. ✅ **竞赛指导教师命名规则**：姓名-职工号，如"张三-114514"
2. ✅ **简化指导教师分配DocType**：只留下instructor字段，字段属性Link不变
3. ✅ **具体赛事指导教师名单**：数据类型修改为Table Multiselect
4. ✅ **报名表单指导教师选择**：从Table Multiselect中选择

## 🔧 具体修改内容

### 1. 竞赛指导教师命名规则修改

#### A. 修改autoname方法
```python
# 文件：competition_regulation_competition_instructor.py
def autoname(self):
    """
    自动命名：使用姓名-职工号格式
    """
    if self.instructor_name and self.employee_id:
        self.name = f"{self.instructor_name}-{self.employee_id}"
```

**效果**：
- 原来：使用职工号作为主键（如"114514"）
- 现在：使用"姓名-职工号"格式（如"张三-114514"）

### 2. 简化指导教师分配DocType

#### A. 修改JSON字段定义
```json
// 文件：competition_regulation_competition_instructor_assignment.json
{
    "field_order": [
        "instructor"
    ],
    "fields": [
        {
            "fieldname": "instructor",
            "fieldtype": "Link",
            "in_list_view": 1,
            "label": "指导教师",
            "options": "Competition Regulation Competition Instructor",
            "reqd": 1
        }
    ]
}
```

**变化**：
- ❌ 移除：`instructor_name`字段（自动获取的教师姓名）
- ❌ 移除：`assignment_type`字段（分配方式）
- ✅ 保留：`instructor`字段（Link类型）

### 3. 具体赛事指导教师名单修改

#### A. 修改字段类型
```json
// 文件：competition_regulation_competition.json
{
    "fieldname": "instructors",
    "fieldtype": "Table MultiSelect",  // 从"Table"改为"Table MultiSelect"
    "label": "指导教师名单",
    "options": "Competition Regulation Competition Instructor"  // 直接引用指导教师主表
}
```

**变化**：
- 字段类型：`Table` → `Table MultiSelect`
- 选项来源：`Competition Regulation Competition Instructor Assignment` → `Competition Regulation Competition Instructor`

### 4. 报名表单指导教师选择逻辑修改

#### A. 更新查询函数
```python
# 文件：competition_regulation_registration.py
@frappe.whitelist()
def get_competition_instructors(doctype, txt, searchfield, start, page_len, filters):
    # 从赛事的指导教师Table MultiSelect中获取指导教师ID列表
    instructor_ids = []
    if competition_doc.instructors:
        for instructor_row in competition_doc.instructors:
            if instructor_row.instructor:
                instructor_ids.append(instructor_row.instructor)
    
    # 查询指导教师信息
    query = """
        SELECT name, instructor_name, college, employee_id
        FROM `tabCompetition Regulation Competition Instructor`
        WHERE name IN (...)
        ORDER BY instructor_name
    """
```

#### B. 更新验证逻辑
```python
def validate_instructor(self):
    # 获取赛事的指导教师ID列表（从Table MultiSelect中）
    valid_instructor_ids = []
    if competition_doc.instructors:
        for instructor_row in competition_doc.instructors:
            if instructor_row.instructor:
                valid_instructor_ids.append(instructor_row.instructor)
```

## 📊 数据结构对比

### 修改前的数据结构
```
Competition Regulation Competition (具体赛事)
    ↓ (has child table)
Competition Regulation Competition Instructor Assignment (指导教师分配子表)
    ├── instructor (Link)
    ├── instructor_name (Data, fetch_from)
    └── assignment_type (Select)
    ↓ (links to)
Competition Regulation Competition Instructor (指导教师主表)
    ├── name = employee_id (如"114514")
    ├── instructor_name
    ├── employee_id
    └── college
```

### 修改后的数据结构
```
Competition Regulation Competition (具体赛事)
    ↓ (has Table MultiSelect)
Competition Regulation Competition Instructor (指导教师主表)
    ├── name = instructor_name-employee_id (如"张三-114514")
    ├── instructor_name
    ├── employee_id
    └── college
```

## 🔍 关键改进

### 1. 简化数据关系
- **移除中间层**：不再需要指导教师分配子表作为中间层
- **直接关联**：具体赛事直接通过Table MultiSelect关联指导教师
- **减少复杂性**：数据流更加直观和简单

### 2. 改进命名规则
- **更直观**：指导教师名称包含姓名和职工号
- **易识别**：用户可以直接看到教师姓名
- **唯一性**：保持职工号的唯一性约束

### 3. 优化用户体验
- **Table MultiSelect**：更直观的多选界面
- **简化配置**：减少不必要的字段
- **统一逻辑**：查询和验证逻辑更加一致

## 🧪 测试验证

### 1. 指导教师命名测试
```python
# 创建指导教师
instructor = frappe.new_doc("Competition Regulation Competition Instructor")
instructor.instructor_name = "张三"
instructor.employee_id = "114514"
instructor.college = "信息学院"
instructor.save()

# 验证命名结果
assert instructor.name == "张三-114514"
```

### 2. Table MultiSelect测试
```python
# 创建赛事并分配指导教师
competition = frappe.new_doc("Competition Regulation Competition")
competition.competition_title = "测试赛事"
competition.append("instructors", {"instructor": "张三-114514"})
competition.append("instructors", {"instructor": "李四-114515"})
competition.save()

# 验证指导教师列表
assert len(competition.instructors) == 2
```

### 3. 报名指导教师选择测试
```python
# 测试查询函数
result = get_competition_instructors(
    "Competition Regulation Competition Instructor",
    "",
    "instructor_name",
    0,
    20,
    '{"competition": "测试赛事"}'
)

# 验证返回结果
assert len(result) == 2
assert result[0][0] == "张三-114514"  # name
assert result[0][1] == "张三"        # instructor_name
```

## ✅ 修改完成状态

- ✅ 修改了竞赛指导教师的命名规则
- ✅ 简化了指导教师分配DocType结构
- ✅ 更新了具体赛事的指导教师字段类型
- ✅ 修改了报名表单的查询和验证逻辑
- ✅ 更新了相关的Python代码逻辑
- ✅ 保持了数据完整性和业务逻辑

### 修改的文件
1. `competition_regulation_competition_instructor.py` - 添加autoname方法
2. `competition_regulation_competition_instructor_assignment.json` - 简化字段结构
3. `competition_regulation_competition.json` - 修改指导教师字段类型
4. `competition_regulation_competition.py` - 更新验证逻辑
5. `competition_regulation_registration.py` - 更新查询和验证逻辑

**现在指导教师的数据结构更加简洁直观，用户体验得到显著改善！**
