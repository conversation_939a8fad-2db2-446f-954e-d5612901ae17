# -*- coding: utf-8 -*-
# Copyright (c) 2025, 1 and Contributors
# See license.txt
from __future__ import unicode_literals

import frappe
import unittest

class TestCompetitionRegulationStudentInfo(unittest.TestCase):
	"""
	学生信息DocType测试类
	
	测试内容：
	- 基本CRUD操作
	- 数据验证
	- 权限控制
	- 业务逻辑
	"""
	
	def setUp(self):
		"""
		测试前准备
		"""
		# 清理测试数据
		frappe.db.delete("Competition Regulation Student Info", {"student_id": ["like", "TEST%"]})
		frappe.db.commit()
	
	def tearDown(self):
		"""
		测试后清理
		"""
		# 清理测试数据
		frappe.db.delete("Competition Regulation Student Info", {"student_id": ["like", "TEST%"]})
		frappe.db.commit()
	
	def test_create_student_info(self):
		"""
		测试创建学生信息
		"""
		# 创建测试学生信息
		student = frappe.get_doc({
			"doctype": "Competition Regulation Student Info",
			"student_id": "TEST20210001",
			"student_name": "张三",
			"gender": "男",
			"email": "<EMAIL>",
			"college": "计算机科学与技术学院",
			"major": "计算机科学与技术",
			"class_name": "计科2021-1班",
			"mobile_phone": "13800138001"
		})
		student.insert()
		
		# 验证创建成功
		self.assertEqual(student.student_id, "TEST20210001")
		self.assertEqual(student.student_name, "张三")
		self.assertEqual(student.gender, "男")
		self.assertEqual(student.email, "<EMAIL>")
		self.assertTrue(student.name)
		
		# 验证数据库中存在
		saved_student = frappe.get_doc("Competition Regulation Student Info", student.name)
		self.assertEqual(saved_student.student_name, "张三")
	
	def test_student_id_validation(self):
		"""
		测试学号验证
		"""
		# 测试空学号
		with self.assertRaises(frappe.ValidationError):
			student = frappe.get_doc({
				"doctype": "Competition Regulation Student Info",
				"student_id": "",
				"student_name": "李四",
				"gender": "女",
				"email": "<EMAIL>",
				"college": "测试学院",
				"major": "测试专业",
				"class_name": "测试班级",
				"mobile_phone": "13800138002"
			})
			student.insert()
		
		# 测试学号过短
		with self.assertRaises(frappe.ValidationError):
			student = frappe.get_doc({
				"doctype": "Competition Regulation Student Info",
				"student_id": "T123",
				"student_name": "李四",
				"gender": "女",
				"email": "<EMAIL>",
				"college": "测试学院",
				"major": "测试专业",
				"class_name": "测试班级",
				"mobile_phone": "13800138002"
			})
			student.insert()
		
		# 测试学号包含特殊字符
		with self.assertRaises(frappe.ValidationError):
			student = frappe.get_doc({
				"doctype": "Competition Regulation Student Info",
				"student_id": "TEST@2021",
				"student_name": "李四",
				"gender": "女",
				"email": "<EMAIL>",
				"college": "测试学院",
				"major": "测试专业",
				"class_name": "测试班级",
				"mobile_phone": "13800138002"
			})
			student.insert()
	
	def test_student_id_uniqueness(self):
		"""
		测试学号唯一性
		"""
		# 创建第一个学生
		student1 = frappe.get_doc({
			"doctype": "Competition Regulation Student Info",
			"student_id": "TEST20210002",
			"student_name": "王五",
			"gender": "男",
			"email": "<EMAIL>",
			"college": "测试学院",
			"major": "测试专业",
			"class_name": "测试班级",
			"mobile_phone": "13800138003"
		})
		student1.insert()
		
		# 尝试创建同学号学生
		with self.assertRaises(frappe.DuplicateEntryError):
			student2 = frappe.get_doc({
				"doctype": "Competition Regulation Student Info",
				"student_id": "TEST20210002",
				"student_name": "赵六",
				"gender": "女",
				"email": "<EMAIL>",
				"college": "另一个学院",
				"major": "另一个专业",
				"class_name": "另一个班级",
				"mobile_phone": "13800138004"
			})
			student2.insert()
	
	def test_student_name_validation(self):
		"""
		测试学生姓名验证
		"""
		# 测试空姓名
		with self.assertRaises(frappe.ValidationError):
			student = frappe.get_doc({
				"doctype": "Competition Regulation Student Info",
				"student_id": "TEST20210003",
				"student_name": "",
				"gender": "男",
				"email": "<EMAIL>",
				"college": "测试学院",
				"major": "测试专业",
				"class_name": "测试班级",
				"mobile_phone": "13800138005"
			})
			student.insert()
		
		# 测试姓名过短
		with self.assertRaises(frappe.ValidationError):
			student = frappe.get_doc({
				"doctype": "Competition Regulation Student Info",
				"student_id": "TEST20210004",
				"student_name": "李",
				"gender": "男",
				"email": "<EMAIL>",
				"college": "测试学院",
				"major": "测试专业",
				"class_name": "测试班级",
				"mobile_phone": "13800138005"
			})
			student.insert()
	
	def test_email_validation(self):
		"""
		测试邮箱验证
		"""
		# 测试空邮箱
		with self.assertRaises(frappe.ValidationError):
			student = frappe.get_doc({
				"doctype": "Competition Regulation Student Info",
				"student_id": "TEST20210005",
				"student_name": "孙七",
				"gender": "女",
				"email": "",
				"college": "测试学院",
				"major": "测试专业",
				"class_name": "测试班级",
				"mobile_phone": "13800138006"
			})
			student.insert()
		
		# 测试邮箱格式错误
		with self.assertRaises(frappe.ValidationError):
			student = frappe.get_doc({
				"doctype": "Competition Regulation Student Info",
				"student_id": "TEST20210006",
				"student_name": "孙七",
				"gender": "女",
				"email": "invalid-email",
				"college": "测试学院",
				"major": "测试专业",
				"class_name": "测试班级",
				"mobile_phone": "13800138006"
			})
			student.insert()
	
	def test_mobile_phone_validation(self):
		"""
		测试手机号码验证
		"""
		# 测试空手机号
		with self.assertRaises(frappe.ValidationError):
			student = frappe.get_doc({
				"doctype": "Competition Regulation Student Info",
				"student_id": "TEST20210007",
				"student_name": "周八",
				"gender": "男",
				"email": "<EMAIL>",
				"college": "测试学院",
				"major": "测试专业",
				"class_name": "测试班级",
				"mobile_phone": ""
			})
			student.insert()
		
		# 测试手机号格式错误
		with self.assertRaises(frappe.ValidationError):
			student = frappe.get_doc({
				"doctype": "Competition Regulation Student Info",
				"student_id": "TEST20210008",
				"student_name": "周八",
				"gender": "男",
				"email": "<EMAIL>",
				"college": "测试学院",
				"major": "测试专业",
				"class_name": "测试班级",
				"mobile_phone": "12345678901"
			})
			student.insert()
	
	def test_data_trimming_and_formatting(self):
		"""
		测试数据自动去除空格和格式化
		"""
		# 创建带空格的学生信息
		student = frappe.get_doc({
			"doctype": "Competition Regulation Student Info",
			"student_id": "  test20210009  ",
			"student_name": "  吴九  ",
			"gender": "女",
			"email": "  <EMAIL>  ",
			"college": "  测试学院  ",
			"major": "  测试专业  ",
			"class_name": "  测试班级  ",
			"mobile_phone": "  13800138009  "
		})
		student.insert()
		
		# 验证空格被自动去除，格式被标准化
		self.assertEqual(student.student_id, "TEST20210009")
		self.assertEqual(student.student_name, "吴九")
		self.assertEqual(student.email, "<EMAIL>")
		self.assertEqual(student.college, "测试学院")
		self.assertEqual(student.major, "测试专业")
		self.assertEqual(student.class_name, "测试班级")
		self.assertEqual(student.mobile_phone, "13800138009")

	def test_update_student_info(self):
		"""
		测试更新学生信息
		"""
		# 创建学生
		student = frappe.get_doc({
			"doctype": "Competition Regulation Student Info",
			"student_id": "TEST20210010",
			"student_name": "郑十",
			"gender": "男",
			"email": "<EMAIL>",
			"college": "原学院",
			"major": "原专业",
			"class_name": "原班级",
			"mobile_phone": "13800138010"
		})
		student.insert()

		# 更新学生信息（模拟学生只能修改特定字段）
		student.college = "新学院"
		student.major = "新专业"
		student.class_name = "新班级"
		student.mobile_phone = "13800138011"
		student.save()

		# 验证更新成功
		updated_student = frappe.get_doc("Competition Regulation Student Info", student.name)
		self.assertEqual(updated_student.college, "新学院")
		self.assertEqual(updated_student.major, "新专业")
		self.assertEqual(updated_student.class_name, "新班级")
		self.assertEqual(updated_student.mobile_phone, "13800138011")
		# 验证不可修改字段未变
		self.assertEqual(updated_student.student_id, "TEST20210010")
		self.assertEqual(updated_student.student_name, "郑十")

	def test_delete_student_info(self):
		"""
		测试删除学生信息
		"""
		# 创建学生
		student = frappe.get_doc({
			"doctype": "Competition Regulation Student Info",
			"student_id": "TEST20210011",
			"student_name": "王十一",
			"gender": "女",
			"email": "<EMAIL>",
			"college": "测试学院",
			"major": "测试专业",
			"class_name": "测试班级",
			"mobile_phone": "13800138012"
		})
		student.insert()
		student_name = student.name

		# 删除学生
		student.delete()

		# 验证删除成功
		self.assertFalse(frappe.db.exists("Competition Regulation Student Info", student_name))

	def test_get_full_info(self):
		"""
		测试获取完整信息方法
		"""
		# 创建学生
		student = frappe.get_doc({
			"doctype": "Competition Regulation Student Info",
			"student_id": "TEST20210012",
			"student_name": "李十二",
			"gender": "男",
			"email": "<EMAIL>",
			"college": "测试学院",
			"major": "测试专业",
			"class_name": "测试班级",
			"mobile_phone": "13800138013"
		})
		student.insert()

		# 测试获取完整信息
		full_info = student.get_full_info()
		expected_info = "李十二（TEST20210012）- 测试学院 测试专业 测试班级"
		self.assertEqual(full_info, expected_info)

	def test_autoname_functionality(self):
		"""
		测试自动命名功能
		"""
		# 创建学生
		student = frappe.get_doc({
			"doctype": "Competition Regulation Student Info",
			"student_id": "TEST20210013",
			"student_name": "张十三",
			"gender": "女",
			"email": "<EMAIL>",
			"college": "测试学院",
			"major": "测试专业",
			"class_name": "测试班级",
			"mobile_phone": "13800138014"
		})
		student.insert()

		# 验证文档名称与学号一致
		self.assertEqual(student.name, "TEST20210013")

	def test_required_fields(self):
		"""
		测试必填字段验证
		"""
		required_fields = [
			'student_id', 'student_name', 'gender', 'email',
			'college', 'major', 'class_name', 'mobile_phone'
		]

		base_data = {
			"doctype": "Competition Regulation Student Info",
			"student_id": "TEST20210014",
			"student_name": "测试学生",
			"gender": "男",
			"email": "<EMAIL>",
			"college": "测试学院",
			"major": "测试专业",
			"class_name": "测试班级",
			"mobile_phone": "13800138015"
		}

		# 测试每个必填字段
		for field in required_fields:
			test_data = base_data.copy()
			test_data[field] = ""  # 设置为空
			test_data["student_id"] = f"TEST2021{field.upper()}"  # 确保学号唯一

			with self.assertRaises(frappe.ValidationError):
				student = frappe.get_doc(test_data)
				student.insert()
