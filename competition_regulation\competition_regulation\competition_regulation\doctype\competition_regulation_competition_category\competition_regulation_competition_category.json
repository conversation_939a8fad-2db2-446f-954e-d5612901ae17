{"actions": [], "allow_rename": 1, "autoname": "field:category_name", "creation": "2025-07-23 00:00:00.000000", "default_view": "List", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["category_name", "competition_level", "projects_section", "projects_text"], "fields": [{"fieldname": "category_name", "fieldtype": "Data", "in_list_view": 1, "label": "赛事名称", "reqd": 1, "unique": 1}, {"fieldname": "competition_level", "fieldtype": "Link", "in_list_view": 1, "label": "赛事级别", "options": "Competition Regulation Competition Level", "reqd": 1}, {"fieldname": "projects_section", "fieldtype": "Section Break", "label": "项目列表"}, {"description": "请输入项目名称，每行一个项目，例如：\nC++\nJava\nPython", "fieldname": "projects_text", "fieldtype": "Long Text", "label": "项目列表", "reqd": 1}], "index_web_pages_for_search": 1, "links": [], "modified": "2025-07-23 00:00:00.000000", "modified_by": "Administrator", "module": "Competition Regulation", "name": "Competition Regulation Competition Category", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "竞赛负责教师", "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "教务处教师", "share": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "学生", "share": 1}], "quick_entry": 0, "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}