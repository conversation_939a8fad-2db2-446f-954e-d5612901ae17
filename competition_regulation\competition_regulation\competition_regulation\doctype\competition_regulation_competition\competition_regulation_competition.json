{"actions": [], "allow_rename": 1, "autoname": "field:competition_title", "creation": "2025-07-23 00:00:00.000000", "default_view": "List", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["basic_info_section", "competition_title", "competition_category", "project_name", "column_break_3", "participation_type", "team_size_limit", "status", "time_section", "registration_start_datetime", "registration_end_datetime", "column_break_8", "competition_start_datetime", "competition_end_datetime", "instructor_section", "instructors", "auto_assign_instructor", "awards_section", "awards_setting", "competition_description", "statistics_section", "total_teams", "total_participants", "column_break_17", "awarded_teams", "awarded_participants"], "fields": [{"fieldname": "basic_info_section", "fieldtype": "Section Break", "label": "基本信息"}, {"fieldname": "competition_title", "fieldtype": "Data", "in_list_view": 1, "label": "赛事标题", "reqd": 1, "unique": 1}, {"fieldname": "competition_category", "fieldtype": "Link", "in_list_view": 1, "label": "赛事种类", "options": "Competition Regulation Competition Category", "reqd": 1}, {"fieldname": "project_name", "fieldtype": "Select", "in_list_view": 1, "label": "项目名称", "reqd": 1}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"fieldname": "participation_type", "fieldtype": "Select", "in_list_view": 1, "label": "参赛形式", "options": "个人赛\n团队赛", "reqd": 1}, {"depends_on": "eval:doc.participation_type=='团队赛'", "fieldname": "team_size_limit", "fieldtype": "Int", "label": "团队人数限制"}, {"default": "报名未开始", "fieldname": "status", "fieldtype": "Select", "in_list_view": 1, "label": "状态", "options": "报名未开始\n报名中\n报名结束\n比赛中\n比赛结束", "read_only": 1}, {"fieldname": "time_section", "fieldtype": "Section Break", "label": "时间安排"}, {"fieldname": "registration_start_datetime", "fieldtype": "Datetime", "label": "报名开始时间", "reqd": 1}, {"fieldname": "registration_end_datetime", "fieldtype": "Datetime", "label": "报名结束时间", "reqd": 1}, {"fieldname": "column_break_8", "fieldtype": "Column Break"}, {"fieldname": "competition_start_datetime", "fieldtype": "Datetime", "label": "比赛开始时间", "reqd": 1}, {"fieldname": "competition_end_datetime", "fieldtype": "Datetime", "label": "比赛结束时间", "reqd": 1}, {"fieldname": "instructor_section", "fieldtype": "Section Break", "label": "指导教师"}, {"fieldname": "instructors", "fieldtype": "Table", "label": "指导教师名单", "options": "Competition Regulation Competition Instructor Assignment"}, {"default": 0, "fieldname": "auto_assign_instructor", "fieldtype": "Check", "label": "自动分配指导教师"}, {"fieldname": "awards_section", "fieldtype": "Section Break", "label": "奖项和介绍"}, {"fieldname": "awards_setting", "fieldtype": "Text Editor", "label": "奖项设置"}, {"fieldname": "competition_description", "fieldtype": "Text Editor", "label": "赛事介绍"}, {"fieldname": "statistics_section", "fieldtype": "Section Break", "label": "统计信息"}, {"default": 0, "fieldname": "total_teams", "fieldtype": "Int", "label": "参赛队伍数量", "read_only": 1}, {"default": 0, "fieldname": "total_participants", "fieldtype": "Int", "label": "参赛人次", "read_only": 1}, {"fieldname": "column_break_17", "fieldtype": "Column Break"}, {"default": 0, "fieldname": "awarded_teams", "fieldtype": "Int", "label": "获奖队伍数量", "read_only": 1}, {"default": 0, "fieldname": "awarded_participants", "fieldtype": "Int", "label": "获奖人次", "read_only": 1}], "index_web_pages_for_search": 1, "links": [], "modified": "2025-07-23 00:00:00.000000", "modified_by": "Administrator", "module": "Competition Regulation", "name": "Competition Regulation Competition", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "竞赛负责教师", "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "教务处教师", "share": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "学生", "share": 1}], "quick_entry": 0, "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}