// Copyright (c) 2025, 1 and contributors
// For license information, please see license.txt

frappe.ui.form.on('Competition Regulation Settings', {
	// 表单刷新时触发
	refresh: function(frm) {
		// 设置字段描述
		frm.set_df_property('competition_reminder_email_template', 'description', 
			'比赛前发送给参赛者的提醒邮件模板，支持HTML格式');
		frm.set_df_property('registration_success_email_template', 'description', 
			'报名成功后发送给参赛者的通知邮件模板，支持HTML格式');
		frm.set_df_property('registration_cancel_email_template', 'description', 
			'取消报名后发送给参赛者的通知邮件模板，支持HTML格式');
		
		// 添加预览按钮
		add_preview_buttons(frm);
		
		// 添加重置按钮
		add_reset_buttons(frm);
		
		// 设置表单标题
		frm.set_title_field('name');
	},
	
	// 比赛提醒邮件模板变化时触发
	competition_reminder_email_template: function(frm) {
		validate_template_variables(frm, 'competition_reminder_email_template', '比赛提醒邮件模板');
	},
	
	// 报名成功邮件模板变化时触发
	registration_success_email_template: function(frm) {
		validate_template_variables(frm, 'registration_success_email_template', '报名成功邮件模板');
	},
	
	// 取消报名邮件模板变化时触发
	registration_cancel_email_template: function(frm) {
		validate_template_variables(frm, 'registration_cancel_email_template', '取消报名邮件模板');
	}
});

// 添加预览按钮
function add_preview_buttons(frm) {
	// 比赛提醒邮件预览
	frm.add_custom_button('预览比赛提醒邮件', function() {
		preview_template(frm, 'reminder', '比赛提醒邮件预览');
	}, '邮件预览');
	
	// 报名成功邮件预览
	frm.add_custom_button('预览报名成功邮件', function() {
		preview_template(frm, 'success', '报名成功邮件预览');
	}, '邮件预览');
	
	// 取消报名邮件预览
	frm.add_custom_button('预览取消报名邮件', function() {
		preview_template(frm, 'cancel', '取消报名邮件预览');
	}, '邮件预览');
}

// 添加重置按钮
function add_reset_buttons(frm) {
	// 重置比赛提醒邮件模板
	frm.add_custom_button('重置比赛提醒模板', function() {
		reset_template(frm, 'competition_reminder_email_template', '比赛提醒邮件模板');
	}, '重置模板');
	
	// 重置报名成功邮件模板
	frm.add_custom_button('重置报名成功模板', function() {
		reset_template(frm, 'registration_success_email_template', '报名成功邮件模板');
	}, '重置模板');
	
	// 重置取消报名邮件模板
	frm.add_custom_button('重置取消报名模板', function() {
		reset_template(frm, 'registration_cancel_email_template', '取消报名邮件模板');
	}, '重置模板');
}

// 预览邮件模板
function preview_template(frm, template_type, title) {
	frappe.call({
		method: 'preview_template',
		doc: frm.doc,
		args: {
			template_type: template_type
		},
		callback: function(r) {
			if (r.message) {
				// 创建预览对话框
				var dialog = new frappe.ui.Dialog({
					title: title,
					size: 'large',
					fields: [
						{
							fieldtype: 'HTML',
							fieldname: 'preview_content',
							options: '<div class="email-preview" style="border: 1px solid #ddd; padding: 15px; background: #fff; min-height: 400px;">' + r.message + '</div>'
						}
					],
					primary_action: function() {
						dialog.hide();
					},
					primary_action_label: '关闭'
				});
				dialog.show();
			} else {
				frappe.msgprint('模板内容为空，无法预览');
			}
		}
	});
}

// 重置邮件模板
function reset_template(frm, field_name, template_name) {
	frappe.confirm(
		'确定要重置' + template_name + '为默认模板吗？当前内容将被覆盖。',
		function() {
			// 清空字段，触发默认模板设置
			frm.set_value(field_name, '');
			
			// 保存文档以触发默认模板设置
			frm.save().then(function() {
				frappe.msgprint(template_name + '已重置为默认模板');
				frm.reload_doc();
			});
		}
	);
}

// 验证模板变量
function validate_template_variables(frm, field_name, template_name) {
	var template_content = frm.doc[field_name];
	if (!template_content) {
		return;
	}
	
	// 定义允许的变量
	var allowed_variables = [
		'student_name', 'student_id', 'team_name', 'competition_name',
		'competition_project', 'competition_level', 'registration_datetime',
		'competition_start_datetime', 'competition_end_datetime',
		'instructor_name', 'college'
	];
	
	// 查找所有变量
	var variable_regex = /\{\{\s*(\w+)\s*\}\}/g;
	var matches;
	var invalid_variables = [];
	
	while ((matches = variable_regex.exec(template_content)) !== null) {
		var variable = matches[1];
		if (!allowed_variables.includes(variable)) {
			invalid_variables.push(variable);
		}
	}
	
	// 显示警告
	if (invalid_variables.length > 0) {
		frappe.msgprint({
			title: '模板变量警告',
			indicator: 'yellow',
			message: template_name + '中包含无效的模板变量：' + invalid_variables.join(', ') + 
					'<br><br>请检查变量名称是否正确，或参考"可用变量"说明。'
		});
	}
}

// 插入模板变量的辅助函数
function insert_variable(frm, field_name, variable) {
	var current_content = frm.doc[field_name] || '';
	var variable_text = '{{ ' + variable + ' }}';
	
	// 在光标位置插入变量（简单实现）
	frm.set_value(field_name, current_content + variable_text);
}

// 添加快速插入变量的功能
frappe.ui.form.on('Competition Regulation Settings', {
	onload: function(frm) {
		// 为每个模板字段添加快速插入按钮
		add_variable_insert_buttons(frm);
	}
});

// 添加变量插入按钮
function add_variable_insert_buttons(frm) {
	var variables = [
		{name: 'student_name', label: '学生姓名'},
		{name: 'student_id', label: '学号'},
		{name: 'team_name', label: '团队名称'},
		{name: 'competition_name', label: '赛事名称'},
		{name: 'competition_project', label: '比赛项目'},
		{name: 'competition_level', label: '赛事级别'},
		{name: 'registration_datetime', label: '报名时间'},
		{name: 'competition_start_datetime', label: '比赛开始时间'},
		{name: 'competition_end_datetime', label: '比赛结束时间'},
		{name: 'instructor_name', label: '指导教师'},
		{name: 'college', label: '学院'}
	];
	
	// 为每个变量添加快速插入按钮
	variables.forEach(function(variable) {
		frm.add_custom_button(variable.label, function() {
			show_insert_variable_dialog(frm, variable);
		}, '插入变量');
	});
}

// 显示插入变量对话框
function show_insert_variable_dialog(frm, variable) {
	var dialog = new frappe.ui.Dialog({
		title: '插入变量：' + variable.label,
		fields: [
			{
				fieldtype: 'Select',
				fieldname: 'target_field',
				label: '目标模板',
				options: [
					{value: 'competition_reminder_email_template', label: '比赛提醒邮件模板'},
					{value: 'registration_success_email_template', label: '报名成功邮件模板'},
					{value: 'registration_cancel_email_template', label: '取消报名邮件模板'}
				],
				reqd: 1
			}
		],
		primary_action: function() {
			var values = dialog.get_values();
			if (values.target_field) {
				insert_variable(frm, values.target_field, variable.name);
				dialog.hide();
				frappe.msgprint('变量已插入到' + dialog.fields_dict.target_field.df.options.find(o => o.value === values.target_field).label);
			}
		},
		primary_action_label: '插入'
	});
	dialog.show();
}
