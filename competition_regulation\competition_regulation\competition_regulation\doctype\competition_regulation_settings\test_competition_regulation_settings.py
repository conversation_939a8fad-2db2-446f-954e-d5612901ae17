# -*- coding: utf-8 -*-
# Copyright (c) 2025, 1 and Contributors
# See license.txt
from __future__ import unicode_literals

import frappe
import unittest

class TestCompetitionRegulationSettings(unittest.TestCase):
	"""
	全局设置DocType测试类
	
	测试内容：
	- 单例模式验证
	- 邮件模板管理
	- 模板变量验证
	- 邮件发送功能
	"""
	
	def setUp(self):
		"""
		测试前准备
		"""
		# 设置测试标志
		frappe.flags.in_test = True
		
		# 清理现有设置
		if frappe.db.exists("Competition Regulation Settings", "Competition Regulation Settings"):
			frappe.delete_doc("Competition Regulation Settings", "Competition Regulation Settings")
		
		frappe.db.commit()
	
	def tearDown(self):
		"""
		测试后清理
		"""
		frappe.flags.in_test = False
	
	def test_create_settings(self):
		"""
		测试创建全局设置
		"""
		# 创建设置
		settings = frappe.get_doc({
			"doctype": "Competition Regulation Settings"
		})
		settings.insert()
		
		# 验证创建成功
		self.assertEqual(settings.name, "Competition Regulation Settings")
		self.assertTrue(settings.competition_reminder_email_template)  # 应该有默认模板
		self.assertTrue(settings.registration_success_email_template)
		self.assertTrue(settings.registration_cancel_email_template)
	
	def test_singleton_behavior(self):
		"""
		测试单例模式行为
		"""
		# 创建第一个设置
		settings1 = frappe.get_doc({
			"doctype": "Competition Regulation Settings"
		})
		settings1.insert()
		
		# 尝试创建第二个设置（应该失败或返回同一个）
		settings2 = frappe.get_single("Competition Regulation Settings")
		
		# 验证是同一个文档
		self.assertEqual(settings1.name, settings2.name)
	
	def test_default_templates(self):
		"""
		测试默认模板设置
		"""
		# 创建设置
		settings = frappe.get_doc({
			"doctype": "Competition Regulation Settings"
		})
		settings.insert()
		
		# 验证默认模板不为空
		self.assertIsNotNone(settings.competition_reminder_email_template)
		self.assertIsNotNone(settings.registration_success_email_template)
		self.assertIsNotNone(settings.registration_cancel_email_template)
		
		# 验证默认模板包含必要的变量
		self.assertIn("{{ student_name }}", settings.competition_reminder_email_template)
		self.assertIn("{{ team_name }}", settings.registration_success_email_template)
		self.assertIn("{{ competition_name }}", settings.registration_cancel_email_template)
	
	def test_template_variable_validation(self):
		"""
		测试模板变量验证
		"""
		# 创建包含无效变量的设置
		with self.assertRaises(frappe.ValidationError):
			settings = frappe.get_doc({
				"doctype": "Competition Regulation Settings",
				"competition_reminder_email_template": "Hello {{ invalid_variable }}!"
			})
			settings.insert()
	
	def test_valid_template_variables(self):
		"""
		测试有效的模板变量
		"""
		# 创建包含有效变量的设置
		settings = frappe.get_doc({
			"doctype": "Competition Regulation Settings",
			"competition_reminder_email_template": """
			Hello {{ student_name }}!
			Your team {{ team_name }} is registered for {{ competition_name }}.
			Competition starts at {{ competition_start_datetime }}.
			""",
			"registration_success_email_template": """
			Congratulations {{ student_name }}!
			Registration successful for {{ competition_project }}.
			""",
			"registration_cancel_email_template": """
			Dear {{ student_name }},
			Your registration for {{ competition_name }} has been cancelled.
			"""
		})
		settings.insert()
		
		# 验证创建成功（没有抛出异常）
		self.assertEqual(settings.name, "Competition Regulation Settings")
	
	def test_template_rendering(self):
		"""
		测试模板渲染
		"""
		# 创建设置
		settings = frappe.get_doc({
			"doctype": "Competition Regulation Settings",
			"competition_reminder_email_template": "Hello {{ student_name }}, your team is {{ team_name }}."
		})
		settings.insert()
		
		# 测试模板渲染
		test_data = {
			"student_name": "张三",
			"team_name": "测试团队"
		}
		
		rendered = settings.render_template(settings.competition_reminder_email_template, test_data)
		expected = "Hello 张三, your team is 测试团队."
		
		self.assertEqual(rendered, expected)
	
	def test_template_rendering_with_missing_data(self):
		"""
		测试缺少数据时的模板渲染
		"""
		# 创建设置
		settings = frappe.get_doc({
			"doctype": "Competition Regulation Settings",
			"competition_reminder_email_template": "Hello {{ student_name }}, your team is {{ team_name }}."
		})
		settings.insert()
		
		# 测试缺少数据的模板渲染
		test_data = {
			"student_name": "张三"
			# team_name 缺失
		}
		
		rendered = settings.render_template(settings.competition_reminder_email_template, test_data)
		expected = "Hello 张三, your team is ."
		
		self.assertEqual(rendered, expected)
	
	def test_get_settings_static_method(self):
		"""
		测试获取设置的静态方法
		"""
		# 创建设置
		original_settings = frappe.get_doc({
			"doctype": "Competition Regulation Settings"
		})
		original_settings.insert()
		
		# 使用静态方法获取设置
		retrieved_settings = CompetitionRegulationSettings.get_settings()
		
		# 验证获取的是同一个设置
		self.assertEqual(original_settings.name, retrieved_settings.name)
	
	def test_preview_template(self):
		"""
		测试模板预览功能
		"""
		# 创建设置
		settings = frappe.get_doc({
			"doctype": "Competition Regulation Settings"
		})
		settings.insert()
		
		# 测试预览比赛提醒模板
		preview_content = settings.preview_template("reminder")
		self.assertIsNotNone(preview_content)
		self.assertIn("张三", preview_content)  # 应该包含示例数据
		
		# 测试预览报名成功模板
		preview_content = settings.preview_template("success")
		self.assertIsNotNone(preview_content)
		self.assertIn("示例团队", preview_content)
		
		# 测试预览取消报名模板
		preview_content = settings.preview_template("cancel")
		self.assertIsNotNone(preview_content)
		self.assertIn("示例竞赛", preview_content)
	
	def test_invalid_preview_template_type(self):
		"""
		测试无效的预览模板类型
		"""
		# 创建设置
		settings = frappe.get_doc({
			"doctype": "Competition Regulation Settings"
		})
		settings.insert()
		
		# 测试无效的模板类型
		with self.assertRaises(frappe.ValidationError):
			settings.preview_template("invalid_type")
	
	def test_prepare_email_data(self):
		"""
		测试准备邮件数据功能
		"""
		# 这个测试需要创建相关的测试数据
		# 由于依赖其他DocType，这里只做基本的方法存在性测试
		
		# 验证静态方法存在
		self.assertTrue(hasattr(CompetitionRegulationSettings, 'prepare_email_data'))
		self.assertTrue(hasattr(CompetitionRegulationSettings, 'send_competition_reminder_email'))
		self.assertTrue(hasattr(CompetitionRegulationSettings, 'send_registration_success_email'))
		self.assertTrue(hasattr(CompetitionRegulationSettings, 'send_registration_cancel_email'))
	
	def test_template_syntax_validation(self):
		"""
		测试模板语法验证
		"""
		# 创建设置
		settings = frappe.get_doc({
			"doctype": "Competition Regulation Settings"
		})
		
		# 测试有效的模板语法
		valid_template = "Hello {{ student_name }}, welcome to {{ competition_name }}!"
		settings.validate_template_syntax(valid_template, "测试模板")  # 不应该抛出异常
		
		# 测试无效的模板语法
		invalid_template = "Hello {{ invalid_var }}, welcome!"
		with self.assertRaises(frappe.ValidationError):
			settings.validate_template_syntax(invalid_template, "测试模板")
	
	def test_custom_template_content(self):
		"""
		测试自定义模板内容
		"""
		# 创建带自定义模板的设置
		custom_template = """
		<p>亲爱的 {{ student_name }} 同学：</p>
		<p>您的团队 {{ team_name }} 已成功报名 {{ competition_name }}。</p>
		<p>比赛时间：{{ competition_start_datetime }}</p>
		"""
		
		settings = frappe.get_doc({
			"doctype": "Competition Regulation Settings",
			"registration_success_email_template": custom_template
		})
		settings.insert()
		
		# 验证自定义模板保存成功
		self.assertEqual(settings.registration_success_email_template, custom_template)
		
		# 测试渲染自定义模板
		test_data = {
			"student_name": "李四",
			"team_name": "创新团队",
			"competition_name": "编程大赛",
			"competition_start_datetime": "2025-08-01 09:00:00"
		}
		
		rendered = settings.render_template(custom_template, test_data)
		self.assertIn("李四", rendered)
		self.assertIn("创新团队", rendered)
		self.assertIn("编程大赛", rendered)
		self.assertIn("2025-08-01 09:00:00", rendered)
