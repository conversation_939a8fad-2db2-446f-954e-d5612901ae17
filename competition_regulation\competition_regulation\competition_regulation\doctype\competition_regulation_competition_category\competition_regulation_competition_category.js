// Copyright (c) 2025, 1 and contributors
// For license information, please see license.txt

frappe.ui.form.on('Competition Regulation Competition Category', {
	// 表单刷新时触发
	refresh: function(frm) {
		// 设置字段描述
		frm.set_df_property('category_name', 'description', '请输入赛事种类名称，如"ACM程序设计竞赛"');
		frm.set_df_property('projects', 'description', '请添加该赛事种类包含的项目');
		
		// 如果是新文档，设置焦点到赛事名称字段
		if (frm.is_new()) {
			frm.set_focus('category_name');
		}
		
		// 添加自定义按钮
		if (!frm.is_new()) {
			add_custom_buttons(frm);
		}
	},
	
	// 赛事名称字段变化时触发
	category_name: function(frm) {
		// 自动去除首尾空格
		if (frm.doc.category_name) {
			frm.set_value('category_name', frm.doc.category_name.trim());
		}
	}
});

// 项目子表事件
frappe.ui.form.on('Competition Regulation Competition Category Project', {
	// 项目名称变化时触发
	project_name: function(frm, cdt, cdn) {
		var row = locals[cdt][cdn];
		if (row.project_name) {
			// 自动去除首尾空格
			frappe.model.set_value(cdt, cdn, 'project_name', row.project_name.trim());
		}
	}
});

// 添加自定义按钮
function add_custom_buttons(frm) {
	// 查看使用此种类的具体赛事
	frm.add_custom_button('查看相关赛事', function() {
		frappe.route_options = {
			"competition_category": frm.doc.name
		};
		frappe.set_route("List", "Competition Regulation Competition");
	});
	
	// 获取项目列表
	frm.add_custom_button('获取项目列表', function() {
		frappe.call({
			method: 'get_projects',
			doc: frm.doc,
			callback: function(r) {
				if (r.message && r.message.length > 0) {
					frappe.msgprint({
						title: '项目列表',
						message: '该赛事种类包含的项目：<br>' + r.message.join('<br>')
					});
				} else {
					frappe.msgprint('该赛事种类暂无项目');
				}
			}
		});
	});
}
