# 奖项方法错误修复总结

## 🎯 问题分析

报错信息：`AttributeError: 'CompetitionRegulationCompetitionCategory' object has no attribute 'get_awards_list'`

### 问题根源
在新的报名DocType中，错误地尝试从赛事种类DocType中获取奖项信息，但赛事种类DocType中没有`get_awards_list()`方法。

## 🔍 问题定位

### 1. 错误的调用路径
```python
# 错误的逻辑（在报名DocType中）
competition_doc = frappe.get_doc("Competition Regulation Competition", competition)
category_doc = frappe.get_doc("Competition Regulation Competition Category", competition_doc.competition_category)
awards_list = category_doc.get_awards_list()  # ❌ 方法不存在
```

### 2. 数据结构分析
- **赛事种类DocType**：只有`projects_text`字段和`get_projects_list()`方法
- **具体赛事DocType**：有`awards_setting`字段，但缺少解析方法
- **报名DocType**：需要获取奖项列表用于动态选项

## 🔧 修复方案

### 1. 在具体赛事DocType中添加奖项解析方法

#### A. 添加`get_awards_list()`方法
```python
def get_awards_list(self):
    """
    从奖项设置中解析奖项列表
    """
    if not self.awards_setting:
        return []
    
    awards_list = []
    awards_text = self.awards_setting
    
    # 移除HTML标签
    import re
    awards_text = re.sub(r'<[^>]*>', '', awards_text)
    
    # 按行分割，解析奖项
    lines = awards_text.split('\n')
    for line in lines:
        award_name = line.strip()
        if award_name:
            # 检查是否包含奖项关键词
            if '奖' in award_name or 'Award' in award_name or 'Prize' in award_name:
                # 去除序号、符号等
                clean_award_name = re.sub(r'^[\d\.\-\*\s\u2022\u25cf]+', '', award_name).strip()
                # 去除末尾的冒号和数字描述
                clean_award_name = re.sub(r'[：:]\s*\d+.*$', '', clean_award_name).strip()
                
                if clean_award_name and clean_award_name not in awards_list:
                    awards_list.append(clean_award_name)
    
    return awards_list
```

#### B. 更新`on_trash()`方法
```python
def on_trash(self):
    """
    删除前检查
    """
    # 检查是否有报名记录引用此赛事
    existing_registrations = frappe.get_all("Competition Regulation Registration", 
        filters={"competition": self.name})
    
    if existing_registrations:
        frappe.throw(_("无法删除赛事，还有{0}个报名记录正在使用此赛事").format(len(existing_registrations)))
```

### 2. 简化报名DocType中的奖项获取逻辑

#### A. 修复前（复杂且错误）
```python
@frappe.whitelist()
def get_competition_awards(competition):
    competition_doc = frappe.get_doc("Competition Regulation Competition", competition)
    if competition_doc.competition_category:
        category_doc = frappe.get_doc("Competition Regulation Competition Category", competition_doc.competition_category)
        awards_list = category_doc.get_awards_list()  # ❌ 方法不存在
        # ... 复杂的解析逻辑
    return awards_list
```

#### B. 修复后（简洁且正确）
```python
@frappe.whitelist()
def get_competition_awards(competition):
    """
    获取赛事的奖项列表
    """
    if not competition:
        return ["未获奖"]
    
    competition_doc = frappe.get_doc("Competition Regulation Competition", competition)
    awards_list = competition_doc.get_awards_list()  # ✅ 调用正确的方法
    
    # 添加"未获奖"选项
    if "未获奖" not in awards_list:
        awards_list.append("未获奖")
    
    return awards_list
```

### 3. 添加缺失的辅助方法

#### A. 在学生信息DocType中添加获取当前用户信息的方法
```python
@frappe.whitelist()
def get_current_user_student_info():
    """
    获取当前用户的学生信息
    """
    current_user = frappe.session.user
    
    # 首先通过用户ID查找
    student_info = frappe.db.get_value("Competition Regulation Student Info",
        {"user_id": current_user}, ["name", "student_name", "student_id", "college"], as_dict=True)
    
    if student_info:
        return student_info
    
    # 如果通过用户ID没找到，尝试通过邮箱匹配
    student_info = frappe.db.get_value("Competition Regulation Student Info",
        {"email": current_user}, ["name", "student_name", "student_id", "college"], as_dict=True)
    
    return student_info
```

## 📊 修复对比

### 修复前的问题
| 问题 | 影响 |
|------|------|
| 调用不存在的方法 | 运行时错误 |
| 错误的数据源 | 逻辑混乱 |
| 重复的解析代码 | 代码冗余 |
| 缺少辅助方法 | 功能不完整 |

### 修复后的改进
| 改进 | 效果 |
|------|------|
| 正确的方法调用 | 消除运行时错误 |
| 清晰的数据流 | 逻辑简洁明了 |
| 复用解析逻辑 | 代码简洁 |
| 完整的辅助方法 | 功能完整 |

## 🔍 数据流分析

### 正确的奖项数据流
```
具体赛事.awards_setting (HTML文本)
    ↓
具体赛事.get_awards_list() (解析方法)
    ↓
报名.get_competition_awards() (API方法)
    ↓
前端动态选项 (JavaScript)
```

### 正确的学生信息流
```
当前用户 (frappe.session.user)
    ↓
学生信息.get_current_user_student_info() (查找方法)
    ↓
报名.add_current_user_as_captain() (自动添加队长)
    ↓
前端表格显示 (JavaScript)
```

## 🧪 测试验证

### 1. 奖项解析测试
```python
# 测试具体赛事的奖项解析
competition = frappe.get_doc("Competition Regulation Competition", "COMP-001")
awards = competition.get_awards_list()
print(f"解析的奖项: {awards}")

# 测试报名API
awards_api = get_competition_awards("COMP-001")
print(f"API返回的奖项: {awards_api}")
```

### 2. 学生信息测试
```python
# 测试当前用户学生信息获取
student_info = get_current_user_student_info()
print(f"当前用户学生信息: {student_info}")
```

## ✅ 修复完成状态

- ✅ 修复了`get_awards_list()`方法不存在的错误
- ✅ 在具体赛事DocType中添加了奖项解析方法
- ✅ 简化了报名DocType中的奖项获取逻辑
- ✅ 添加了获取当前用户学生信息的方法
- ✅ 更新了删除前检查逻辑
- ✅ 确保了数据流的正确性

### 修复的文件
1. `competition_regulation_competition.py` - 添加奖项解析方法
2. `competition_regulation_registration.py` - 简化奖项获取逻辑
3. `competition_regulation_student_info.py` - 添加用户信息获取方法

**现在所有方法调用都是正确的，不会再出现AttributeError错误！**
