# -*- coding: utf-8 -*-
# Copyright (c) 2025, 1 and contributors
# For license information, please see license.txt

from __future__ import unicode_literals
import frappe
from frappe.model.document import Document
from frappe import _

class CompetitionRegulationCompetitionCategory(Document):
	"""
	赛事种类DocType控制器
	
	功能：
	- 管理赛事的基本种类信息
	- 维护每个赛事种类的项目列表
	- 提供给具体赛事选择使用
	"""
	
	def autoname(self):
		"""
		自动命名
		"""
		if self.category_name:
			self.category_name = self.category_name.strip()
			self.name = self.category_name

	def validate(self):
		"""
		验证数据有效性
		"""
		self.validate_category_name()
		self.validate_projects()
	
	def validate_category_name(self):
		"""
		验证赛事名称
		"""
		if self.category_name:
			self.category_name = self.category_name.strip()
			
		if not self.category_name:
			frappe.throw(_("赛事名称不能为空"))
		
		# 验证赛事名称长度
		if len(self.category_name) < 2 or len(self.category_name) > 100:
			frappe.throw(_("赛事名称长度应在2-100个字符之间"))
	
	def validate_projects(self):
		"""
		验证项目列表
		"""
		if not self.projects_text or not self.projects_text.strip():
			frappe.throw(_("项目列表不能为空"))

		# 解析项目列表
		projects = self.get_projects_list()

		if len(projects) == 0:
			frappe.throw(_("项目列表必须包含至少一个项目"))

		# 验证项目名称不能重复
		project_names = []
		for project_name in projects:
			if project_name in project_names:
				frappe.throw(_("项目名称不能重复：{0}").format(project_name))
			project_names.append(project_name)

	def get_projects_list(self):
		"""
		从文本框中解析项目列表
		"""
		if not self.projects_text:
			return []

		# 按行分割，去除空行和首尾空格
		projects = []
		for line in self.projects_text.split('\n'):
			project_name = line.strip()
			if project_name:  # 忽略空行
				projects.append(project_name)

		return projects
	
	def before_save(self):
		"""
		保存前处理
		"""
		# 标准化赛事名称格式
		if self.category_name:
			self.category_name = self.category_name.strip()
	
	def on_update(self):
		"""
		更新后处理
		"""
		pass
	
	def on_trash(self):
		"""
		删除前检查
		检查是否有具体赛事正在使用此种类
		"""
		# 检查是否有具体赛事引用此种类
		existing_competitions = frappe.get_all("Competition Regulation Competition", 
			filters={"competition_category": self.name})
		
		if existing_competitions:
			frappe.throw(_("无法删除赛事种类，还有{0}个具体赛事正在使用此种类").format(len(existing_competitions)))
	
	@frappe.whitelist()
	def get_projects(self):
		"""
		获取该赛事种类的所有项目
		返回项目名称列表
		"""
		return [project.project_name for project in self.projects]
