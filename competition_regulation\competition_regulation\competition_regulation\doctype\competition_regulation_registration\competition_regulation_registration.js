// Copyright (c) 2025, 1 and contributors
// For license information, please see license.txt

frappe.ui.form.on('Competition Regulation Registration', {
	refresh: function(frm) {
		// 设置字段权限
		setup_field_permissions(frm);

		// 设置动态选项
		setup_dynamic_options(frm);

		// 设置指导教师选项（始终设置，确保约束生效）
		setup_instructor_options(frm);

		// 添加自定义按钮
		add_custom_buttons(frm);

		// 设置参赛人员表格
		setup_participants_grid(frm);

		// 如果是新建表单，自动添加当前用户为队长
		if (frm.is_new() && (!frm.doc.participants || frm.doc.participants.length === 0)) {
			add_current_user_as_captain(frm);
		}
	},

	// 赛事变化时触发
	competition: function(frm) {
		if (frm.doc.competition) {
			// 获取赛事信息并自动填充
			frappe.call({
				method: 'frappe.client.get',
				args: {
					doctype: 'Competition Regulation Competition',
					name: frm.doc.competition
				},
				callback: function(r) {
					if (r.message) {
						var competition = r.message;

						// 自动填充参赛形式和人数限制
						frm.set_value('participation_type', competition.participation_type);
						frm.set_value('team_size_limit', competition.team_size_limit);

						// 设置项目选项
						setup_project_options(frm, competition.competition_category);

						// 设置奖项选项
						setup_award_options(frm, competition.competition_category);

						// 重新设置指导教师选项（重要：限制为当前赛事的指导教师）
						setup_instructor_options(frm);

						// 清空之前选择的指导教师（因为可能不在新赛事的指导教师名单中）
						frm.set_value('instructor', '');
					}
				}
			});
		} else {
			// 清空相关字段
			frm.set_value('participation_type', '');
			frm.set_value('team_size_limit', '');
			frm.set_value('project_name', '');
			frm.set_value('instructor', '');
		}
	},

	// 项目名称变化时触发
	project_name: function(frm) {
		// 可以在这里添加项目相关的逻辑
	},

	// 指导教师变化时触发
	instructor: function(frm) {
		// 如果所有队员都已同意且有指导教师，更新状态
		if (frm.doc.instructor && frm.doc.status === '等待分配指导教师') {
			frm.set_value('status', '成功报名');
		}
	}
});

// 设置字段权限
function setup_field_permissions(frm) {
	var user_roles = frappe.user_roles;
	
	// 学生角色的字段权限
	if (user_roles.includes('学生')) {
		// 所获奖项对学生只读
		frm.set_df_property('award', 'read_only', 1);
		
		// 状态对学生只读
		frm.set_df_property('status', 'read_only', 1);
		
		// 检查比赛是否已开始，如果是则整个表单只读
		if (frm.doc.competition) {
			frappe.call({
				method: 'frappe.client.get',
				args: {
					doctype: 'Competition Regulation Competition',
					name: frm.doc.competition
				},
				callback: function(r) {
					if (r.message) {
						var competition = r.message;
						var now = new Date();
						var start_time = new Date(competition.competition_start_datetime);
						
						if (now > start_time) {
							// 比赛已开始，设置表单只读
							frm.set_read_only();
							frappe.show_alert({
								message: '比赛已开始，无法修改报名信息',
								indicator: 'orange'
							});
						}
					}
				}
			});
		}
	}
}

// 设置动态选项
function setup_dynamic_options(frm) {
	// 设置赛事选项（只显示"报名中"状态的赛事）
	frm.set_query('competition', function() {
		return {
			filters: {
				'status': '报名中'
			}
		};
	});
	
	// 设置学生信息选择
	frm.set_query('student_id', 'participants', function(doc, cdt, cdn) {
		return {
			query: 'competition_regulation.competition_regulation.doctype.competition_regulation_student_info.competition_regulation_student_info.get_student_list_for_selection',
			filters: {}
		};
	});
}

// 设置项目选项
function setup_project_options(frm, category) {
	if (category) {
		frappe.call({
			method: 'competition_regulation.competition_regulation.doctype.competition_regulation_registration.competition_regulation_registration.get_competition_projects',
			args: {
				competition: frm.doc.competition
			},
			callback: function(r) {
				if (r.message) {
					var options = r.message.join('\n');
					frm.set_df_property('project_name', 'options', options);
				}
			}
		});
	}
}

// 设置奖项选项
function setup_award_options(frm, category) {
	if (category) {
		frappe.call({
			method: 'competition_regulation.competition_regulation.doctype.competition_regulation_registration.competition_regulation_registration.get_competition_awards',
			args: {
				competition: frm.doc.competition
			},
			callback: function(r) {
				if (r.message) {
					var options = r.message.join('\n');
					frm.set_df_property('award', 'options', options);
				}
			}
		});
	}
}

// 设置指导教师选项
function setup_instructor_options(frm) {
	// 设置指导教师查询，限制为所选赛事的指导教师
	frm.set_query('instructor', function() {
		if (!frm.doc.competition) {
			frappe.msgprint('请先选择报名赛事');
			return {
				filters: {
					'name': 'no-match'  // 返回空结果
				}
			};
		}

		return {
			query: 'competition_regulation.competition_regulation.doctype.competition_regulation_registration.competition_regulation_registration.get_competition_instructors',
			filters: {
				'competition': frm.doc.competition
			}
		};
	});
}

// 添加自定义按钮
function add_custom_buttons(frm) {
	var user_roles = frappe.user_roles;
	
	// 学生角色的按钮
	if (user_roles.includes('学生') && !frm.is_new()) {
		// 邀请队员按钮
		if (frm.doc.status === '等待同意' && frm.doc.participation_type === '团队赛') {
			frm.add_custom_button(__('邀请队员'), function() {
				invite_participant_dialog(frm);
			});
		}
		
		// 响应邀请按钮
		if (frm.doc.status === '等待同意') {
			var current_user_student = get_current_user_student_id();
			var has_pending_invitation = false;
			
			if (frm.doc.participants) {
				frm.doc.participants.forEach(function(participant) {
					if (participant.student_id === current_user_student && 
						participant.invitation_status === '等待回应') {
						has_pending_invitation = true;
					}
				});
			}
			
			if (has_pending_invitation) {
				frm.add_custom_button(__('接受邀请'), function() {
					respond_invitation(frm, true);
				}, __('邀请响应'));
				
				frm.add_custom_button(__('拒绝邀请'), function() {
					respond_invitation(frm, false);
				}, __('邀请响应'));
			}
		}
		
		// 取消报名按钮
		if (['等待同意', '等待分配指导教师', '成功报名'].includes(frm.doc.status)) {
			frm.add_custom_button(__('取消报名'), function() {
				frappe.confirm(
					'确定要取消报名吗？此操作不可撤销。',
					function() {
						frm.call('cancel_registration');
					}
				);
			});
		}
	}
	
	// 竞赛负责教师的按钮
	if (user_roles.includes('竞赛负责教师') && !frm.is_new()) {
		// 分配指导教师按钮
		if (frm.doc.status === '等待分配指导教师') {
			frm.add_custom_button(__('分配指导教师'), function() {
				assign_instructor_dialog(frm);
			});
		}
		
		// 填写获奖信息按钮
		if (frm.doc.status === '成功报名') {
			frm.add_custom_button(__('填写获奖信息'), function() {
				award_dialog(frm);
			});
		}
	}
}

// 设置参赛人员表格
function setup_participants_grid(frm) {
	// 设置第一行（队长）为只读
	if (frm.doc.participants && frm.doc.participants.length > 0) {
		setTimeout(function() {
			setup_captain_row_readonly(frm);
		}, 100);
	}
}

// 设置队长行只读
function setup_captain_row_readonly(frm) {
	var grid = frm.fields_dict['participants'].grid;
	if (grid.grid_rows && grid.grid_rows[0]) {
		var first_row = grid.grid_rows[0];
		
		// 设置关键字段为只读
		if (first_row.columns.student_id) {
			first_row.columns.student_id.df.read_only = 1;
		}
		if (first_row.columns.is_captain) {
			first_row.columns.is_captain.df.read_only = 1;
		}
		if (first_row.columns.invitation_status) {
			first_row.columns.invitation_status.df.read_only = 1;
		}
		
		first_row.refresh();
	}
}

// 添加当前用户为队长
function add_current_user_as_captain(frm) {
	frappe.call({
		method: 'competition_regulation.competition_regulation.doctype.competition_regulation_student_info.competition_regulation_student_info.get_current_user_student_info',
		callback: function(r) {
			if (r.message) {
				var student = r.message;
				
				// 确保至少有一行
				if (!frm.doc.participants || frm.doc.participants.length === 0) {
					frm.add_child('participants');
				}
				
				// 设置第一行为队长
				var first_participant = frm.doc.participants[0];
				first_participant.student_id = student.name;
				first_participant.student_name = student.student_name;
				first_participant.college = student.college;
				first_participant.invitation_status = '已接受';
				first_participant.is_captain = 1;
				
				// 设置队长字段
				frm.set_value('captain', student.name);
				
				// 刷新表格
				frm.refresh_field('participants');
				
				// 设置第一行只读
				setTimeout(function() {
					setup_captain_row_readonly(frm);
				}, 100);
			} else {
				frappe.msgprint({
					title: '学生信息缺失',
					message: '未找到您的学生信息，请先完善学生资料。',
					indicator: 'red'
				});
			}
		}
	});
}

// 邀请参赛人员对话框
function invite_participant_dialog(frm) {
	var dialog = new frappe.ui.Dialog({
		title: '邀请队员',
		fields: [
			{
				fieldtype: 'Link',
				fieldname: 'student_id',
				label: '学生',
				options: 'Competition Regulation Student Info',
				reqd: 1,
				get_query: function() {
					return {
						query: 'competition_regulation.competition_regulation.doctype.competition_regulation_student_info.competition_regulation_student_info.get_student_list_for_selection',
						filters: {}
					};
				}
			}
		],
		primary_action_label: '发送邀请',
		primary_action: function(values) {
			frm.call('invite_participant', {
				student_id: values.student_id
			}).then(function() {
				dialog.hide();
				frm.reload_doc();
			});
		}
	});
	
	dialog.show();
}

// 响应邀请
function respond_invitation(frm, accept) {
	frm.call('respond_invitation', {
		accept: accept
	}).then(function() {
		frm.reload_doc();
	});
}

// 分配指导教师对话框
function assign_instructor_dialog(frm) {
	var dialog = new frappe.ui.Dialog({
		title: '分配指导教师',
		fields: [
			{
				fieldtype: 'Link',
				fieldname: 'instructor',
				label: '指导教师',
				options: 'Competition Regulation Competition Instructor',
				reqd: 1,
				get_query: function() {
					return {
						query: 'competition_regulation.competition_regulation.doctype.competition_regulation_registration.competition_regulation_registration.get_competition_instructors',
						filters: {
							competition: frm.doc.competition
						}
					};
				}
			}
		],
		primary_action_label: '分配',
		primary_action: function(values) {
			frm.set_value('instructor', values.instructor);
			frm.set_value('status', '成功报名');
			frm.save().then(function() {
				dialog.hide();
				frappe.show_alert({
					message: '指导教师分配成功',
					indicator: 'green'
				});
			});
		}
	});
	
	dialog.show();
}

// 获奖信息对话框
function award_dialog(frm) {
	var dialog = new frappe.ui.Dialog({
		title: '填写获奖信息',
		fields: [
			{
				fieldtype: 'Select',
				fieldname: 'award',
				label: '获奖情况',
				options: frm.get_field('award').df.options,
				reqd: 1
			}
		],
		primary_action_label: '保存',
		primary_action: function(values) {
			frm.set_value('award', values.award);
			
			// 根据获奖情况设置状态
			if (values.award === '未获奖') {
				frm.set_value('status', '未获奖');
			} else {
				frm.set_value('status', '获奖');
			}
			
			frm.save().then(function() {
				dialog.hide();
				frappe.show_alert({
					message: '获奖信息已保存',
					indicator: 'green'
				});
			});
		}
	});
	
	dialog.show();
}

// 获取当前用户的学生ID（辅助函数）
function get_current_user_student_id() {
	// 这里需要实现获取当前用户学生ID的逻辑
	// 可以通过API调用获取
	return null;
}
