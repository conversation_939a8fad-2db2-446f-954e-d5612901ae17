# -*- coding: utf-8 -*-
# Copyright (c) 2025, 1 and contributors
# For license information, please see license.txt

"""
Translation file validation script for Competition Regulation app
"""

import csv
import os

def validate_translations():
    """
    验证翻译文件的完整性和格式
    """
    translation_file = os.path.join(os.path.dirname(__file__), 'translations', 'zh.csv')
    
    if not os.path.exists(translation_file):
        print("❌ Translation file not found!")
        return False
    
    try:
        with open(translation_file, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            lines = list(reader)
        
        # 验证格式
        valid_lines = []
        errors = []
        
        for i, line in enumerate(lines, 1):
            if len(line) != 2:
                errors.append(f"Line {i}: Expected 2 columns, got {len(line)}")
            elif not line[0].strip() or not line[1].strip():
                errors.append(f"Line {i}: Empty translation found")
            else:
                valid_lines.append(line)
        
        if errors:
            print("❌ Format errors found:")
            for error in errors:
                print(f"  {error}")
            return False
        
        # 检查重要翻译
        translations = {line[0]: line[1] for line in valid_lines}
        
        critical_translations = [
            'Competition Regulation',
            'Competition Regulation Workspace',
            'Competition Regulation Competition Level',
            'Competition Regulation Competition Instructor', 
            'Competition Regulation Student Info',
            'Competition Regulation Competition',
            'Competition Regulation Registration',
            'Competition Regulation Settings',
            'Student Name',
            'Team Name',
            'Competition Name',
            'Registration Status',
            'Competition Status'
        ]
        
        missing = []
        for key in critical_translations:
            if key not in translations:
                missing.append(key)
        
        if missing:
            print("❌ Missing critical translations:")
            for key in missing:
                print(f"  {key}")
            return False
        
        print(f"✅ Translation file validation successful!")
        print(f"   Total entries: {len(valid_lines)}")
        print(f"   All critical translations present")
        
        return True
        
    except Exception as e:
        print(f"❌ Error reading translation file: {e}")
        return False

if __name__ == "__main__":
    validate_translations()
