{"actions": [], "allow_rename": 1, "autoname": "field:employee_id", "creation": "2025-07-22 00:00:00.000000", "default_view": "List", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["instructor_name", "employee_id", "college"], "fields": [{"fieldname": "instructor_name", "fieldtype": "Data", "in_list_view": 1, "label": "姓名", "reqd": 1}, {"fieldname": "employee_id", "fieldtype": "Data", "in_list_view": 1, "label": "职工号", "reqd": 1, "unique": 1}, {"fieldname": "college", "fieldtype": "Data", "in_list_view": 1, "label": "学院", "reqd": 1}], "index_web_pages_for_search": 1, "links": [], "modified": "2025-07-22 00:00:00.000000", "modified_by": "Administrator", "module": "Competition Regulation", "name": "Competition Regulation Competition Instructor", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "竞赛负责教师", "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "教务处教师", "share": 1}, {"read": 1, "role": "学生"}], "quick_entry": 0, "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}