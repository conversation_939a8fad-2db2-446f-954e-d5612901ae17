// Copyright (c) 2025, 1 and contributors
// For license information, please see license.txt

frappe.ui.form.on('Competition Regulation Competition Level', {
	// 表单刷新时触发
	refresh: function(frm) {
		// 设置表单标题
		frm.set_df_property('level_name', 'description', '请输入竞赛级别名称，如：国际级、国家级、省级等');
		
		// 如果是新文档，设置焦点到名称字段
		if (frm.is_new()) {
			frm.set_focus('level_name');
		}
	},
	
	// 级别名称字段变化时触发
	level_name: function(frm) {
		// 自动去除首尾空格
		if (frm.doc.level_name) {
			frm.set_value('level_name', frm.doc.level_name.trim());
		}
	},
	
	// 表单验证
	validate: function(frm) {
		// 验证级别名称不能为空
		if (!frm.doc.level_name || frm.doc.level_name.trim() === '') {
			frappe.msgprint({
				title: '验证错误',
				indicator: 'red',
				message: '级别名称不能为空'
			});
			frappe.validated = false;
			return false;
		}
		
		// 验证级别名称长度
		if (frm.doc.level_name.length > 50) {
			frappe.msgprint({
				title: '验证错误',
				indicator: 'red',
				message: '级别名称长度不能超过50个字符'
			});
			frappe.validated = false;
			return false;
		}
	}
});
