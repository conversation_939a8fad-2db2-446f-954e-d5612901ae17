{"actions": [], "allow_rename": 1, "autoname": "naming_series:", "creation": "2025-07-28 00:00:00.000000", "default_view": "List", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["naming_series", "basic_info_section", "competition", "project_name", "participation_type", "team_size_limit", "team_name", "column_break_7", "team_composition", "instructor", "award", "status", "participants_section", "participants", "system_info_section", "captain", "created_by", "created_at"], "fields": [{"default": "REG-.YYYY.-.MM.-.#####", "fieldname": "naming_series", "fieldtype": "Select", "label": "命名系列", "options": "REG-.YYYY.-.MM.-.#####", "reqd": 1}, {"fieldname": "basic_info_section", "fieldtype": "Section Break", "label": "基本信息"}, {"fieldname": "competition", "fieldtype": "Link", "in_list_view": 1, "label": "报名赛事", "options": "Competition Regulation Competition", "reqd": 1}, {"fieldname": "project_name", "fieldtype": "Select", "in_list_view": 1, "label": "报名项目", "reqd": 1}, {"fieldname": "participation_type", "fieldtype": "Data", "label": "参赛形式", "read_only": 1}, {"depends_on": "eval:doc.participation_type=='团队赛'", "fieldname": "team_size_limit", "fieldtype": "Data", "label": "团队人数上限", "read_only": 1}, {"depends_on": "eval:doc.participation_type=='团队赛'", "fieldname": "team_name", "fieldtype": "Data", "in_list_view": 1, "label": "团队名称", "mandatory_depends_on": "eval:doc.participation_type=='团队赛'"}, {"fieldname": "column_break_7", "fieldtype": "Column Break"}, {"fieldname": "team_composition", "fieldtype": "Select", "label": "队员组成成分", "options": "信息学部内\n信息学部外\n混合", "read_only": 1}, {"fieldname": "instructor", "fieldtype": "Link", "get_query": "competition_regulation.competition_regulation.doctype.competition_regulation_registration.competition_regulation_registration.get_competition_instructors", "label": "指导教师", "options": "Competition Regulation Competition Instructor"}, {"fieldname": "award", "fieldtype": "Select", "label": "所获奖项"}, {"default": "等待同意", "fieldname": "status", "fieldtype": "Select", "in_list_view": 1, "label": "状态", "options": "等待同意\n等待分配指导教师\n成功报名\n报名取消\n获奖\n未获奖", "reqd": 1}, {"fieldname": "participants_section", "fieldtype": "Section Break", "label": "参赛人员名单"}, {"fieldname": "participants", "fieldtype": "Table", "label": "参赛人员", "options": "Competition Regulation Registration Participant", "reqd": 1}, {"fieldname": "system_info_section", "fieldtype": "Section Break", "label": "系统信息"}, {"fieldname": "captain", "fieldtype": "Link", "label": "队长", "options": "Competition Regulation Student Info", "read_only": 1}, {"fieldname": "created_by", "fieldtype": "Link", "label": "创建者", "options": "User", "read_only": 1}, {"fieldname": "created_at", "fieldtype": "Datetime", "label": "创建时间", "read_only": 1}], "index_web_pages_for_search": 1, "links": [], "modified": "2025-07-28 00:00:00.000000", "modified_by": "Administrator", "module": "Competition Regulation", "name": "Competition Regulation Registration", "naming_rule": "By \"Naming Series\" field", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "竞赛负责教师", "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "教务处教师", "share": 1}, {"create": 1, "read": 1, "role": "学生", "write": 1}], "quick_entry": 0, "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}