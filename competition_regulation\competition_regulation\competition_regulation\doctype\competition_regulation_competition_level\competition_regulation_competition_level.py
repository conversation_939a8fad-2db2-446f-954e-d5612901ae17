# -*- coding: utf-8 -*-
# Copyright (c) 2025, 1 and contributors
# For license information, please see license.txt

from __future__ import unicode_literals
import frappe
from frappe.model.document import Document

class CompetitionRegulationCompetitionLevel(Document):
	"""
	赛事级别DocType控制器
	
	功能：
	- 管理竞赛的不同级别（如国际级、国家级、省级等）
	- 提供基础的CRUD操作
	- 确保级别名称的唯一性
	"""
	
	def validate(self):
		"""
		验证数据有效性
		"""
		self.validate_level_name()
	
	def validate_level_name(self):
		"""
		验证级别名称
		- 确保名称不为空
		- 去除首尾空格
		"""
		if self.level_name:
			self.level_name = self.level_name.strip()
			
		if not self.level_name:
			frappe.throw("级别名称不能为空")
	
	def before_save(self):
		"""
		保存前处理
		"""
		# 标准化级别名称格式
		if self.level_name:
			self.level_name = self.level_name.strip()
	
	def on_update(self):
		"""
		更新后处理
		"""
		pass
	
	def on_trash(self):
		"""
		删除前检查
		检查是否有竞赛正在使用此级别
		"""
		# 这里可以添加检查逻辑，确保没有竞赛正在使用此级别
		pass
