# -*- coding: utf-8 -*-
# Copyright (c) 2025, 1 and Contributors
# See license.txt
from __future__ import unicode_literals

import frappe
import unittest

class TestCompetitionRegulationCompetitionInstructor(unittest.TestCase):
	"""
	竞赛指导教师信息DocType测试类
	
	测试内容：
	- 基本CRUD操作
	- 数据验证
	- 权限控制
	- 业务逻辑
	"""
	
	def setUp(self):
		"""
		测试前准备
		"""
		# 清理测试数据
		frappe.db.delete("Competition Regulation Competition Instructor", {"employee_id": ["like", "TEST%"]})
		frappe.db.commit()
	
	def tearDown(self):
		"""
		测试后清理
		"""
		# 清理测试数据
		frappe.db.delete("Competition Regulation Competition Instructor", {"employee_id": ["like", "TEST%"]})
		frappe.db.commit()
	
	def test_create_instructor(self):
		"""
		测试创建指导教师
		"""
		# 创建测试教师
		instructor = frappe.get_doc({
			"doctype": "Competition Regulation Competition Instructor",
			"instructor_name": "张三",
			"employee_id": "TEST001",
			"college": "计算机科学与技术学院"
		})
		instructor.insert()
		
		# 验证创建成功
		self.assertEqual(instructor.instructor_name, "张三")
		self.assertEqual(instructor.employee_id, "TEST001")
		self.assertEqual(instructor.college, "计算机科学与技术学院")
		self.assertTrue(instructor.name)
		
		# 验证数据库中存在
		saved_instructor = frappe.get_doc("Competition Regulation Competition Instructor", instructor.name)
		self.assertEqual(saved_instructor.instructor_name, "张三")
	
	def test_instructor_name_validation(self):
		"""
		测试教师姓名验证
		"""
		# 测试空姓名
		with self.assertRaises(frappe.ValidationError):
			instructor = frappe.get_doc({
				"doctype": "Competition Regulation Competition Instructor",
				"instructor_name": "",
				"employee_id": "TEST002",
				"college": "测试学院"
			})
			instructor.insert()
		
		# 测试姓名过短
		with self.assertRaises(frappe.ValidationError):
			instructor = frappe.get_doc({
				"doctype": "Competition Regulation Competition Instructor",
				"instructor_name": "李",
				"employee_id": "TEST003",
				"college": "测试学院"
			})
			instructor.insert()
		
		# 测试姓名过长
		with self.assertRaises(frappe.ValidationError):
			instructor = frappe.get_doc({
				"doctype": "Competition Regulation Competition Instructor",
				"instructor_name": "这是一个非常非常长的姓名测试",
				"employee_id": "TEST004",
				"college": "测试学院"
			})
			instructor.insert()
	
	def test_employee_id_validation(self):
		"""
		测试职工号验证
		"""
		# 测试空职工号
		with self.assertRaises(frappe.ValidationError):
			instructor = frappe.get_doc({
				"doctype": "Competition Regulation Competition Instructor",
				"instructor_name": "王五",
				"employee_id": "",
				"college": "测试学院"
			})
			instructor.insert()
		
		# 测试职工号过短
		with self.assertRaises(frappe.ValidationError):
			instructor = frappe.get_doc({
				"doctype": "Competition Regulation Competition Instructor",
				"instructor_name": "王五",
				"employee_id": "T123",
				"college": "测试学院"
			})
			instructor.insert()
		
		# 测试职工号包含特殊字符
		with self.assertRaises(frappe.ValidationError):
			instructor = frappe.get_doc({
				"doctype": "Competition Regulation Competition Instructor",
				"instructor_name": "王五",
				"employee_id": "TEST@123",
				"college": "测试学院"
			})
			instructor.insert()
	
	def test_employee_id_uniqueness(self):
		"""
		测试职工号唯一性
		"""
		# 创建第一个教师
		instructor1 = frappe.get_doc({
			"doctype": "Competition Regulation Competition Instructor",
			"instructor_name": "赵六",
			"employee_id": "TEST005",
			"college": "测试学院"
		})
		instructor1.insert()
		
		# 尝试创建同职工号教师
		with self.assertRaises(frappe.DuplicateEntryError):
			instructor2 = frappe.get_doc({
				"doctype": "Competition Regulation Competition Instructor",
				"instructor_name": "钱七",
				"employee_id": "TEST005",
				"college": "另一个学院"
			})
			instructor2.insert()
	
	def test_college_validation(self):
		"""
		测试学院验证
		"""
		# 测试空学院
		with self.assertRaises(frappe.ValidationError):
			instructor = frappe.get_doc({
				"doctype": "Competition Regulation Competition Instructor",
				"instructor_name": "孙八",
				"employee_id": "TEST006",
				"college": ""
			})
			instructor.insert()
		
		# 测试学院名称过短
		with self.assertRaises(frappe.ValidationError):
			instructor = frappe.get_doc({
				"doctype": "Competition Regulation Competition Instructor",
				"instructor_name": "孙八",
				"employee_id": "TEST007",
				"college": "院"
			})
			instructor.insert()
	
	def test_data_trimming(self):
		"""
		测试数据自动去除空格
		"""
		# 创建带空格的教师信息
		instructor = frappe.get_doc({
			"doctype": "Competition Regulation Competition Instructor",
			"instructor_name": "  周九  ",
			"employee_id": "  test008  ",
			"college": "  测试学院  "
		})
		instructor.insert()
		
		# 验证空格被自动去除，职工号转为大写
		self.assertEqual(instructor.instructor_name, "周九")
		self.assertEqual(instructor.employee_id, "TEST008")
		self.assertEqual(instructor.college, "测试学院")
	
	def test_update_instructor(self):
		"""
		测试更新指导教师
		"""
		# 创建教师
		instructor = frappe.get_doc({
			"doctype": "Competition Regulation Competition Instructor",
			"instructor_name": "吴十",
			"employee_id": "TEST009",
			"college": "原学院"
		})
		instructor.insert()
		
		# 更新教师信息
		instructor.instructor_name = "吴十一"
		instructor.college = "新学院"
		instructor.save()
		
		# 验证更新成功
		updated_instructor = frappe.get_doc("Competition Regulation Competition Instructor", instructor.name)
		self.assertEqual(updated_instructor.instructor_name, "吴十一")
		self.assertEqual(updated_instructor.college, "新学院")
		self.assertEqual(updated_instructor.employee_id, "TEST009")  # 职工号不变
	
	def test_delete_instructor(self):
		"""
		测试删除指导教师
		"""
		# 创建教师
		instructor = frappe.get_doc({
			"doctype": "Competition Regulation Competition Instructor",
			"instructor_name": "郑十二",
			"employee_id": "TEST010",
			"college": "测试学院"
		})
		instructor.insert()
		instructor_name = instructor.name
		
		# 删除教师
		instructor.delete()
		
		# 验证删除成功
		self.assertFalse(frappe.db.exists("Competition Regulation Competition Instructor", instructor_name))
	
	def test_get_full_info(self):
		"""
		测试获取完整信息方法
		"""
		# 创建教师
		instructor = frappe.get_doc({
			"doctype": "Competition Regulation Competition Instructor",
			"instructor_name": "王十三",
			"employee_id": "TEST011",
			"college": "测试学院"
		})
		instructor.insert()
		
		# 测试获取完整信息
		full_info = instructor.get_full_info()
		expected_info = "王十三（TEST011）- 测试学院"
		self.assertEqual(full_info, expected_info)
	
	def test_autoname_functionality(self):
		"""
		测试自动命名功能
		"""
		# 创建教师
		instructor = frappe.get_doc({
			"doctype": "Competition Regulation Competition Instructor",
			"instructor_name": "李十四",
			"employee_id": "TEST012",
			"college": "测试学院"
		})
		instructor.insert()
		
		# 验证文档名称与职工号一致
		self.assertEqual(instructor.name, "TEST012")
