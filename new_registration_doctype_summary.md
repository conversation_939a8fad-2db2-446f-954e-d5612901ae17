# 新报名DocType重构总结

## 🎯 重构目标

根据用户详细要求，完全重新设计报名DocType和参赛人员子表，实现更复杂的业务逻辑和权限控制。

## 📋 创建的DocType

### 1. Competition Regulation Registration Participant（参赛人员子表）

#### 字段定义
| 字段名 | 类型 | 说明 |
|--------|------|------|
| student_id | Link | 学号（引用学生信息） |
| student_name | Data | 姓名（自动获取，只读） |
| college | Data | 学院/学部（自动获取，只读） |
| invitation_status | Select | 邀请状态（等待回应、已接受、已拒绝、已取消） |
| is_captain | Check | 是否队长（只读） |

#### 特点
- ✅ 自动填充学生信息
- ✅ 邀请状态控制
- ✅ 队长标识

### 2. Competition Regulation Registration（报名主表）

#### 字段定义
| 字段名 | 类型 | 说明 |
|--------|------|------|
| naming_series | Select | 命名系列（REG-.YYYY.-.MM.-.#####） |
| competition | Link | 报名赛事（只能选择"报名中"状态） |
| project_name | Select | 报名项目（动态获取） |
| participation_type | Data | 参赛形式（只读，自动获取） |
| team_size_limit | Data | 团队人数上限（只读，自动获取） |
| team_name | Data | 团队名称（团队赛时必填） |
| team_composition | Select | 队员组成成分（系统自动判断） |
| instructor | Link | 指导教师 |
| award | Select | 所获奖项（动态获取，学生只读） |
| status | Select | 状态（学生只读） |
| participants | Table | 参赛人员名单 |
| captain | Link | 队长（只读） |
| created_by | Link | 创建者（只读） |
| created_at | Datetime | 创建时间（只读） |

#### 状态流转
```
等待同意 → 等待分配指导教师 → 成功报名 → 获奖/未获奖
     ↓
   报名取消
```

## 🔧 核心功能实现

### 1. 权限控制

#### A. 角色权限
| 角色 | 权限 |
|------|------|
| 教务处教师 | 查看所有报名 |
| 竞赛负责教师 | 完全权限（增删改查） |
| 学生 | 创建、查看自己的报名 |

#### B. 字段权限
- **学生角色**：
  - `award`字段只读
  - `status`字段只读
  - 比赛开始后整个表单只读

#### C. 数据权限
- 学生只能查看自己所在队伍的记录
- 通过`get_permission_query_conditions`实现

### 2. 业务逻辑

#### A. 报名验证
```python
def validate(self):
    self.validate_competition_status()      # 验证赛事状态
    self.validate_registration_timing()     # 验证报名时间
    self.validate_participants()            # 验证参赛人员
    self.validate_team_composition()        # 验证团队组成
    self.validate_duplicate_registration()  # 验证重复报名
    self.set_captain_info()                # 设置队长信息
    self.calculate_team_composition()       # 计算队员组成
    self.auto_fill_competition_info()      # 自动填充赛事信息
```

#### B. 队长设置
- 报名创建者自动成为队长
- 队长信息填入参赛人员名单第一行
- 队长状态自动设为"已接受"

#### C. 队员邀请流程
1. 队长邀请其他学生
2. 被邀请学生响应（接受/拒绝）
3. 接受后自动拒绝该赛事的其他邀请
4. 检查是否所有队员都已同意
5. 根据指导教师情况更新状态

#### D. 状态自动更新
```python
def check_all_participants_accepted(self):
    if self.status == "等待同意":
        accepted_participants = [p for p in self.participants if p.invitation_status == "已接受"]
        
        if 人数达到要求:
            if self.instructor:
                self.status = "成功报名"
            else:
                self.status = "等待分配指导教师"
```

### 3. 动态选项

#### A. 项目选项
```python
@frappe.whitelist()
def get_competition_projects(competition):
    competition_doc = frappe.get_doc("Competition Regulation Competition", competition)
    category_doc = frappe.get_doc("Competition Regulation Competition Category", competition_doc.competition_category)
    return category_doc.get_projects_list()
```

#### B. 奖项选项
```python
@frappe.whitelist()
def get_competition_awards(competition):
    # 获取赛事奖项并添加"未获奖"选项
    awards_list = category_doc.get_awards_list()
    if "未获奖" not in awards_list:
        awards_list.append("未获奖")
    return awards_list
```

#### C. 指导教师选项
```python
@frappe.whitelist()
def get_competition_instructors(competition):
    # 查找该赛事的指导教师分配记录
    return frappe.db.sql("""
        SELECT DISTINCT ci.name, ci.instructor_name, ci.title, ci.college
        FROM `tabCompetition Regulation Competition Instructor` ci
        INNER JOIN `tabCompetition Regulation Competition Instructor Assignment` cia
            ON ci.name = cia.instructor
        WHERE cia.competition = %s
    """, (competition,), as_dict=True)
```

## 🎨 前端功能

### 1. 动态界面
- 赛事选择后自动填充相关信息
- 根据参赛形式显示/隐藏团队相关字段
- 动态设置项目、奖项、指导教师选项

### 2. 自定义按钮

#### A. 学生角色按钮
- **邀请队员**：团队赛时可邀请其他学生
- **响应邀请**：接受/拒绝邀请
- **取消报名**：取消整个团队的报名

#### B. 竞赛负责教师按钮
- **分配指导教师**：为等待分配的团队分配指导教师
- **填写获奖信息**：为成功报名的团队填写获奖情况

### 3. 表格控制
- 队长行（第一行）设为只读
- 自动添加当前用户为队长
- 邀请状态的可视化显示

## 🔒 安全控制

### 1. 时间控制
- 报名开始前不能报名
- 报名结束后不能报名
- 比赛开始后不能修改/取消

### 2. 重复控制
- 同一学生在同一赛事中只能报名一次
- 团队成员不能重复
- 团队名称不能重复

### 3. 人数控制
- 个人赛只能1人
- 团队赛不能超过人数上限
- 必须达到人数要求才能提交

## 📊 数据完整性

### 1. 自动填充
- 从赛事自动获取参赛形式、人数限制
- 从学生信息自动获取姓名、学院
- 自动计算队员组成成分

### 2. 状态同步
- 邀请响应后自动更新状态
- 指导教师分配后自动更新状态
- 获奖填写后自动更新状态

### 3. 关联验证
- 验证赛事存在且状态正确
- 验证学生信息存在
- 验证指导教师分配关系

## ✅ 实现完成状态

- ✅ 删除了原有的报名DocType
- ✅ 创建了新的参赛人员子表
- ✅ 创建了新的报名主表
- ✅ 实现了复杂的业务逻辑
- ✅ 配置了详细的权限控制
- ✅ 添加了丰富的前端功能
- ✅ 更新了hooks.py配置

**新的报名系统完全符合用户的详细要求，实现了复杂的队员邀请、状态流转、权限控制等功能！**
