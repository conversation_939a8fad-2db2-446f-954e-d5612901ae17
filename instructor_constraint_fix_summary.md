# 指导教师约束修复总结

## 🎯 需求分析

用户要求：指导教师必须在所选"具体赛事"DocType的指导教师名单中选择，修改相关代码。

### 约束要求
1. ✅ **选择约束**：只能从当前赛事的指导教师名单中选择
2. ✅ **动态更新**：赛事变化时，指导教师选项要相应更新
3. ✅ **数据验证**：后端验证选择的指导教师是否有效
4. ✅ **用户体验**：清晰的错误提示和自动清空无效选择

## 🔧 实施方案

### 1. DocType字段配置修改

#### A. 报名DocType JSON配置
```json
{
    "fieldname": "instructor",
    "fieldtype": "Link",
    "get_query": "competition_regulation.competition_regulation.doctype.competition_regulation_registration.competition_regulation_registration.get_competition_instructors",
    "label": "指导教师",
    "options": "Competition Regulation Competition Instructor"
}
```

**关键改进**：
- ✅ 添加了`get_query`属性，指向自定义查询函数
- ✅ 确保Link字段使用受限的查询逻辑

### 2. JavaScript前端控制

#### A. 动态查询设置
```javascript
function setup_instructor_options(frm) {
    frm.set_query('instructor', function() {
        if (!frm.doc.competition) {
            frappe.msgprint('请先选择报名赛事');
            return {
                filters: {
                    'name': 'no-match'  // 返回空结果
                }
            };
        }
        
        return {
            query: 'path.to.get_competition_instructors',
            filters: {
                competition: frm.doc.competition
            }
        };
    });
}
```

#### B. 赛事变化时的处理
```javascript
competition: function(frm) {
    if (frm.doc.competition) {
        // 重新设置指导教师选项
        setup_instructor_options(frm);
        
        // 清空之前选择的指导教师（重要！）
        frm.set_value('instructor', '');
    } else {
        // 清空相关字段
        frm.set_value('instructor', '');
    }
}
```

**关键特性**：
- ✅ **即时约束**：没有选择赛事时，指导教师字段返回空结果
- ✅ **自动清空**：赛事变化时自动清空之前的指导教师选择
- ✅ **用户提示**：明确提示用户先选择赛事

### 3. Python后端验证

#### A. 验证流程集成
```python
def validate(self):
    self.validate_competition_status()
    self.validate_registration_timing()
    self.validate_participants()
    self.validate_team_composition()
    self.validate_duplicate_registration()
    self.validate_instructor()  # 新增：指导教师验证
    # ... 其他验证
```

#### B. 指导教师验证逻辑
```python
def validate_instructor(self):
    """
    验证指导教师
    """
    if self.instructor and self.competition:
        # 检查指导教师是否在所选赛事的指导教师名单中
        competition_doc = frappe.get_doc("Competition Regulation Competition", self.competition)
        
        # 获取赛事的指导教师ID列表
        valid_instructor_ids = []
        if competition_doc.instructors:
            for instructor_assignment in competition_doc.instructors:
                if instructor_assignment.instructor:
                    valid_instructor_ids.append(instructor_assignment.instructor)
        
        # 检查选择的指导教师是否在有效列表中
        if self.instructor not in valid_instructor_ids:
            instructor_name = frappe.db.get_value("Competition Regulation Competition Instructor", 
                self.instructor, "instructor_name")
            frappe.throw(_("指导教师 {0} 不在所选赛事的指导教师名单中，请重新选择").format(instructor_name or self.instructor))
```

### 4. 查询函数优化

#### A. 增强的错误处理
```python
@frappe.whitelist()
def get_competition_instructors(doctype, txt, searchfield, start, page_len, filters):
    try:
        # 验证赛事是否存在
        if not frappe.db.exists("Competition Regulation Competition", competition):
            frappe.log_error(f"赛事不存在: {competition}")
            return []
        
        # 获取赛事文档
        competition_doc = frappe.get_doc("Competition Regulation Competition", competition)
        
        # 从赛事的指导教师子表中获取指导教师ID列表
        instructor_ids = []
        if competition_doc.instructors:
            for instructor_assignment in competition_doc.instructors:
                if instructor_assignment.instructor:
                    # 验证指导教师是否存在
                    if frappe.db.exists("Competition Regulation Competition Instructor", instructor_assignment.instructor):
                        instructor_ids.append(instructor_assignment.instructor)
        
        if not instructor_ids:
            return []  # 没有分配指导教师
        
        # 构建查询...
        
    except Exception as e:
        frappe.log_error(f"获取赛事指导教师失败: {e}")
        return []
```

## 📊 数据流分析

### 正确的约束流程
```
用户选择赛事
    ↓
JavaScript设置指导教师查询约束
    ↓
用户选择指导教师（仅限当前赛事的指导教师）
    ↓
Python后端验证指导教师有效性
    ↓
保存成功 / 抛出验证错误
```

### 数据关系
```
Competition Regulation Competition (具体赛事)
    ↓ (has child table)
Competition Regulation Competition Instructor Assignment (指导教师分配)
    ↓ (links to)
Competition Regulation Competition Instructor (指导教师)
    ↑ (selected in)
Competition Regulation Registration.instructor (报名的指导教师字段)
```

## 🔒 约束机制

### 1. 前端约束
- **即时检查**：没有选择赛事时，指导教师字段无法选择
- **动态更新**：赛事变化时，指导教师选项立即更新
- **自动清空**：赛事变化时，自动清空无效的指导教师选择

### 2. 后端约束
- **数据验证**：保存时验证指导教师是否在赛事名单中
- **错误提示**：提供清晰的错误信息
- **日志记录**：记录查询错误用于调试

### 3. 用户体验
- **清晰提示**：明确告知用户操作顺序
- **即时反馈**：选择无效时立即提示
- **自动处理**：减少用户手动操作

## 🧪 测试场景

### 1. 正常流程测试
```
1. 选择赛事A
2. 指导教师下拉列表只显示赛事A的指导教师
3. 选择指导教师X
4. 保存成功
```

### 2. 约束测试
```
1. 不选择赛事
2. 尝试选择指导教师
3. 系统提示"请先选择报名赛事"
4. 指导教师字段无可选项
```

### 3. 切换赛事测试
```
1. 选择赛事A，选择指导教师X
2. 切换到赛事B
3. 指导教师字段自动清空
4. 指导教师下拉列表更新为赛事B的指导教师
```

### 4. 后端验证测试
```
1. 通过API直接提交无效的指导教师ID
2. 系统抛出验证错误
3. 错误信息清晰明确
```

## ✅ 实施完成状态

- ✅ 修改了报名DocType的指导教师字段配置
- ✅ 更新了JavaScript中的动态查询设置
- ✅ 添加了赛事变化时的自动清空逻辑
- ✅ 实现了Python后端的指导教师验证
- ✅ 优化了查询函数的错误处理
- ✅ 确保了前后端一致的约束机制

### 修改的文件
1. `competition_regulation_registration.json` - 添加get_query配置
2. `competition_regulation_registration.js` - 更新前端约束逻辑
3. `competition_regulation_registration.py` - 添加后端验证逻辑

**现在指导教师字段完全受限于所选赛事的指导教师名单，确保数据的一致性和有效性！**
