# -*- coding: utf-8 -*-
# Copyright (c) 2025, 1 and contributors
# For license information, please see license.txt

from __future__ import unicode_literals
import frappe

def update_competition_status():
    """
    定时任务：更新所有赛事的状态
    每小时执行一次
    """
    # 导入原始函数并调用
    from competition_regulation.competition_regulation.doctype.competition_regulation_competition.competition_regulation_competition import update_all_competition_status
    return update_all_competition_status()

def send_reminder_emails():
    """
    定时任务：发送比赛提醒邮件
    每小时执行一次
    """
    # 导入原始函数并调用
    from competition_regulation.competition_regulation.doctype.competition_regulation_registration.competition_regulation_registration import send_competition_reminder_emails
    return send_competition_reminder_emails()
