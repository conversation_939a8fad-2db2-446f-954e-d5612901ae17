# -*- coding: utf-8 -*-
# Copyright (c) 2025, 1 and contributors
# For license information, please see license.txt

from __future__ import unicode_literals
import frappe
from frappe.model.document import Document
from frappe import _

class CompetitionRegulationCompetitionInstructorAssignment(Document):
	"""
	赛事指导教师分配DocType控制器（子表）
	
	功能：
	- 管理具体赛事的指导教师分配
	- 支持手动分配和自动分配
	"""
	
	def validate(self):
		"""
		验证数据有效性
		"""
		self.validate_instructor()
	
	def validate_instructor(self):
		"""
		验证指导教师
		"""
		if not self.instructor:
			frappe.throw(_("指导教师不能为空"))
		
		# 验证指导教师是否存在
		if not frappe.db.exists("Competition Regulation Competition Instructor", self.instructor):
			frappe.throw(_("指导教师不存在：{0}").format(self.instructor))
	
	def before_save(self):
		"""
		保存前处理
		"""
		# 自动获取教师姓名
		if self.instructor and not self.instructor_name:
			instructor_doc = frappe.get_doc("Competition Regulation Competition Instructor", self.instructor)
			self.instructor_name = instructor_doc.instructor_name
