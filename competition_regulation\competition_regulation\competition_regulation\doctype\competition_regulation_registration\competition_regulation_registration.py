# -*- coding: utf-8 -*-
# Copyright (c) 2025, 1 and contributors
# For license information, please see license.txt

from __future__ import unicode_literals
import frappe
from frappe.model.document import Document
from frappe import _
import datetime

class CompetitionRegulationRegistration(Document):
	"""
	竞赛报名DocType控制器
	
	功能：
	- 管理竞赛报名流程
	- 处理队员邀请和响应
	- 控制报名状态变更
	- 实现复杂的业务逻辑
	"""
	
	def validate(self):
		"""
		验证数据有效性
		"""
		self.validate_competition_status()
		self.validate_registration_timing()
		self.validate_participants()
		self.validate_team_composition()
		self.validate_duplicate_registration()
		self.validate_instructor()
		self.set_captain_info()
		self.calculate_team_composition()
		self.auto_fill_competition_info()
	
	def validate_competition_status(self):
		"""
		验证赛事状态
		"""
		if self.competition:
			competition_doc = frappe.get_doc("Competition Regulation Competition", self.competition)
			if competition_doc.status != "报名中":
				frappe.throw(_("只能报名状态为'报名中'的赛事"))
	
	def validate_registration_timing(self):
		"""
		验证报名时间
		"""
		if self.competition:
			competition_doc = frappe.get_doc("Competition Regulation Competition", self.competition)
			now = frappe.utils.now_datetime()
			
			# 检查报名是否已开始
			if competition_doc.registration_start_datetime and now < competition_doc.registration_start_datetime:
				frappe.throw(_("报名尚未开始"))
			
			# 检查报名是否已结束
			if competition_doc.registration_end_datetime and now > competition_doc.registration_end_datetime:
				frappe.throw(_("报名已结束"))
	
	def validate_participants(self):
		"""
		验证参赛人员
		"""
		if not self.participants or len(self.participants) == 0:
			frappe.throw(_("必须至少有一名参赛人员"))
		
		# 验证人数限制
		if self.competition:
			competition_doc = frappe.get_doc("Competition Regulation Competition", self.competition)
			
			# 统计已接受邀请的人员
			accepted_participants = [p for p in self.participants if p.invitation_status == "已接受"]
			
			if competition_doc.participation_type == "个人赛":
				if len(accepted_participants) != 1:
					frappe.throw(_("个人赛只能有1名参赛者"))
			elif competition_doc.participation_type == "团队赛":
				if competition_doc.team_size_limit and len(accepted_participants) > competition_doc.team_size_limit:
					frappe.throw(_("团队人数不能超过{0}人").format(competition_doc.team_size_limit))
		
		# 验证参赛人员不能重复
		student_ids = []
		for participant in self.participants:
			if participant.student_id:
				if participant.student_id in student_ids:
					frappe.throw(_("参赛人员不能重复：{0}").format(participant.student_name))
				student_ids.append(participant.student_id)
	
	def validate_team_composition(self):
		"""
		验证团队组成
		"""
		if self.participation_type == "团队赛" and not self.team_name:
			frappe.throw(_("团队赛必须填写团队名称"))
	
	def validate_duplicate_registration(self):
		"""
		验证重复报名
		"""
		for participant in self.participants:
			if participant.invitation_status == "已接受":
				# 检查该学生是否已在本赛事中有其他成功报名
				existing_registrations = frappe.db.sql("""
					SELECT DISTINCT r.name, r.team_name
					FROM `tabCompetition Regulation Registration` r
					INNER JOIN `tabCompetition Regulation Registration Participant` p
						ON r.name = p.parent
					WHERE r.competition = %s
						AND p.student_id = %s
						AND p.invitation_status = '已接受'
						AND r.status NOT IN ('报名取消')
						AND r.name != %s
				""", (self.competition, participant.student_id, self.name or ""))

				if existing_registrations:
					frappe.throw(_("学生{0}已在本赛事的其他团队中报名").format(participant.student_name))

	def validate_instructor(self):
		"""
		验证指导教师
		"""
		if self.instructor and self.competition:
			# 检查指导教师是否在所选赛事的指导教师名单中
			competition_doc = frappe.get_doc("Competition Regulation Competition", self.competition)

			# 获取赛事的指导教师ID列表（从子表中）
			valid_instructor_ids = []
			if competition_doc.instructors:
				for instructor_assignment in competition_doc.instructors:
					if instructor_assignment.instructor:
						valid_instructor_ids.append(instructor_assignment.instructor)

			# 检查选择的指导教师是否在有效列表中
			if self.instructor not in valid_instructor_ids:
				instructor_name = frappe.db.get_value("Competition Regulation Competition Instructor",
					self.instructor, "instructor_name")
				frappe.throw(_("指导教师 {0} 不在所选赛事的指导教师名单中，请重新选择").format(instructor_name or self.instructor))
	
	def set_captain_info(self):
		"""
		设置队长信息
		"""
		# 报名创建者固定为队长
		if self.is_new():
			current_user_student = self.get_current_user_student_info()
			if current_user_student:
				self.captain = current_user_student
				
				# 确保队长在参赛人员名单第一行
				if not self.participants or len(self.participants) == 0:
					self.append("participants", {
						"student_id": current_user_student,
						"invitation_status": "已接受",
						"is_captain": 1
					})
				else:
					# 确保第一行是队长
					first_participant = self.participants[0]
					first_participant.student_id = current_user_student
					first_participant.invitation_status = "已接受"
					first_participant.is_captain = 1
		
		# 确保只有一个队长
		captain_count = 0
		for participant in self.participants:
			if participant.is_captain:
				captain_count += 1
		
		if captain_count == 0:
			frappe.throw(_("必须指定一名队长"))
		elif captain_count > 1:
			frappe.throw(_("只能有一名队长"))
	
	def calculate_team_composition(self):
		"""
		计算队员组成成分
		"""
		if not self.participants:
			return
		
		# 统计已接受邀请的人员的学院信息
		accepted_participants = [p for p in self.participants if p.invitation_status == "已接受"]
		
		if not accepted_participants:
			return
		
		info_school_count = 0
		total_count = len(accepted_participants)
		
		for participant in accepted_participants:
			if participant.student_id:
				student_doc = frappe.get_doc("Competition Regulation Student Info", participant.student_id)
				# 假设信息学部的学院名称包含"信息"
				if "信息" in student_doc.college:
					info_school_count += 1
		
		if info_school_count == total_count:
			self.team_composition = "信息学部内"
		elif info_school_count == 0:
			self.team_composition = "信息学部外"
		else:
			self.team_composition = "混合"
	
	def auto_fill_competition_info(self):
		"""
		自动填充赛事信息
		"""
		if self.competition:
			competition_doc = frappe.get_doc("Competition Regulation Competition", self.competition)
			self.participation_type = competition_doc.participation_type
			self.team_size_limit = competition_doc.team_size_limit
	
	def before_save(self):
		"""
		保存前处理
		"""
		# 设置创建信息
		if self.is_new():
			self.created_by = frappe.session.user
			self.created_at = frappe.utils.now_datetime()
	
	def on_update(self):
		"""
		更新后处理
		"""
		self.check_all_participants_accepted()
	
	def check_all_participants_accepted(self):
		"""
		检查是否所有队员都已同意
		"""
		if self.status == "等待同意":
			# 检查是否达到人数要求且所有人都已接受
			accepted_participants = [p for p in self.participants if p.invitation_status == "已接受"]
			
			if self.participation_type == "个人赛" and len(accepted_participants) == 1:
				self.update_status_after_all_accepted()
			elif self.participation_type == "团队赛":
				competition_doc = frappe.get_doc("Competition Regulation Competition", self.competition)
				if competition_doc.team_size_limit and len(accepted_participants) == competition_doc.team_size_limit:
					self.update_status_after_all_accepted()
	
	def update_status_after_all_accepted(self):
		"""
		所有队员同意后更新状态
		"""
		if self.instructor:
			# 已有指导教师，直接进入成功报名
			self.status = "成功报名"
		else:
			# 没有指导教师，等待分配
			self.status = "等待分配指导教师"
		
		self.save()
	
	def get_current_user_student_info(self):
		"""
		获取当前用户的学生信息
		"""
		current_user = frappe.session.user
		student_info = frappe.db.get_value("Competition Regulation Student Info",
			{"user_id": current_user}, "name")
		
		if not student_info:
			# 尝试通过邮箱匹配
			student_info = frappe.db.get_value("Competition Regulation Student Info",
				{"email": current_user}, "name")
		
		return student_info
	
	@frappe.whitelist()
	def invite_participant(self, student_id):
		"""
		邀请参赛人员
		"""
		# 验证学生信息存在
		if not frappe.db.exists("Competition Regulation Student Info", student_id):
			frappe.throw(_("学生信息不存在"))
		
		# 检查是否已在团队中
		for participant in self.participants:
			if participant.student_id == student_id:
				frappe.throw(_("该学生已在团队中"))
		
		# 检查团队人数限制
		if self.competition:
			competition_doc = frappe.get_doc("Competition Regulation Competition", self.competition)
			if competition_doc.participation_type == "团队赛" and competition_doc.team_size_limit:
				current_count = len(self.participants)
				if current_count >= competition_doc.team_size_limit:
					frappe.throw(_("团队人数已达上限"))
		
		# 添加新参赛人员
		student_doc = frappe.get_doc("Competition Regulation Student Info", student_id)
		self.append("participants", {
			"student_id": student_id,
			"student_name": student_doc.student_name,
			"college": student_doc.college,
			"invitation_status": "等待回应",
			"is_captain": 0
		})
		
		self.save()
		frappe.msgprint(_("邀请已发送"))
	
	@frappe.whitelist()
	def respond_invitation(self, accept=True):
		"""
		响应邀请
		"""
		current_user_student = self.get_current_user_student_info()
		if not current_user_student:
			frappe.throw(_("未找到当前用户的学生信息"))
		
		# 找到当前用户的参赛记录
		participant_found = False
		for participant in self.participants:
			if participant.student_id == current_user_student:
				if participant.invitation_status == "等待回应":
					if accept:
						participant.invitation_status = "已接受"
						# 自动拒绝该赛事中其他所有邀请
						self.reject_other_invitations(current_user_student)
						frappe.msgprint(_("已接受邀请"))
					else:
						participant.invitation_status = "已拒绝"
						frappe.msgprint(_("已拒绝邀请"))
					
					participant_found = True
					break
		
		if not participant_found:
			frappe.throw(_("未找到您的邀请记录"))
		
		self.save()
	
	def reject_other_invitations(self, student_id):
		"""
		拒绝该赛事中的其他所有邀请
		"""
		other_registrations = frappe.get_all("Competition Regulation Registration",
			filters={
				"competition": self.competition,
				"name": ["!=", self.name],
				"status": ["not in", ["报名取消"]]
			})
		
		for reg in other_registrations:
			reg_doc = frappe.get_doc("Competition Regulation Registration", reg.name)
			for participant in reg_doc.participants:
				if participant.student_id == student_id and participant.invitation_status == "等待回应":
					participant.invitation_status = "已拒绝"
			reg_doc.save()
	
	@frappe.whitelist()
	def cancel_registration(self):
		"""
		取消报名
		"""
		# 检查是否可以取消
		if self.competition:
			competition_doc = frappe.get_doc("Competition Regulation Competition", self.competition)
			if competition_doc.competition_start_datetime and frappe.utils.now_datetime() > competition_doc.competition_start_datetime:
				frappe.throw(_("比赛已开始，无法取消报名"))
		
		# 设置所有参赛人员状态为"已取消"
		for participant in self.participants:
			participant.invitation_status = "已取消"
		
		self.status = "报名取消"
		self.save()
		frappe.msgprint(_("报名已取消"))


# 权限查询条件
def get_permission_query_conditions(user):
	"""
	获取权限查询条件
	学生只能查看自己所在队伍的记录
	"""
	if not user:
		user = frappe.session.user

	user_roles = frappe.get_roles(user)

	# 如果是教务处教师或竞赛负责教师，可以查看所有报名
	if "教务处教师" in user_roles or "竞赛负责教师" in user_roles:
		return ""

	# 如果是学生，只能查看自己所在队伍的记录
	if "学生" in user_roles:
		student_info = frappe.db.get_value("Competition Regulation Student Info",
			{"user_id": user}, "name")

		if student_info:
			# 查找包含该学生的所有报名记录
			registrations = frappe.db.sql("""
				SELECT DISTINCT parent
				FROM `tabCompetition Regulation Registration Participant`
				WHERE student_id = %s AND invitation_status = '已接受'
			""", (student_info,))

			if registrations:
				registration_names = [r[0] for r in registrations]
				return f"`tabCompetition Regulation Registration`.name IN ({','.join(['%s'] * len(registration_names))})" % tuple([f"'{name}'" for name in registration_names])

	# 其他情况不允许查看
	return "1=0"


# 权限检查
def has_permission(doc, user):
	"""
	检查是否有权限访问文档
	"""
	if not user:
		user = frappe.session.user

	user_roles = frappe.get_roles(user)

	# 如果是教务处教师或竞赛负责教师，有完全权限
	if "教务处教师" in user_roles or "竞赛负责教师" in user_roles:
		return True

	# 如果是学生
	if "学生" in user_roles:
		student_info = frappe.db.get_value("Competition Regulation Student Info",
			{"user_id": user}, "name")

		if student_info:
			if isinstance(doc, str):
				# 如果传入的是文档名称，需要获取文档
				doc = frappe.get_doc("Competition Regulation Registration", doc)

			# 检查是否是队伍成员
			for participant in doc.participants:
				if participant.student_id == student_info and participant.invitation_status == "已接受":
					return True

		return False

	# 其他情况不允许访问
	return False


@frappe.whitelist()
def get_competition_projects(competition):
	"""
	获取赛事的项目列表
	"""
	if not competition:
		return []

	competition_doc = frappe.get_doc("Competition Regulation Competition", competition)
	if competition_doc.competition_category:
		category_doc = frappe.get_doc("Competition Regulation Competition Category", competition_doc.competition_category)
		return category_doc.get_projects_list()

	return []


@frappe.whitelist()
def get_competition_awards(competition):
	"""
	获取赛事的奖项列表
	"""
	if not competition:
		return ["未获奖"]

	competition_doc = frappe.get_doc("Competition Regulation Competition", competition)
	awards_list = competition_doc.get_awards_list()

	# 添加"未获奖"选项
	if "未获奖" not in awards_list:
		awards_list.append("未获奖")

	return awards_list


@frappe.whitelist()
def get_competition_instructors(doctype, txt, searchfield, start, page_len, filters):
	"""
	获取赛事的指导教师列表
	用于Link字段的自定义查询
	"""
	# 解析过滤条件
	import json

	if isinstance(filters, str):
		filters = json.loads(filters)

	competition = filters.get('competition') if filters else None
	   
	if not competition:
		return []

	try:
		# 验证赛事是否存在
		if not frappe.db.exists("Competition Regulation Competition", competition):
			frappe.log_error(f"赛事不存在: {competition}")
			return []

		# 获取赛事文档
		competition_doc = frappe.get_doc("Competition Regulation Competition", competition)

		# 从赛事的指导教师子表中获取指导教师ID列表
		instructor_ids = []
		if competition_doc.instructors:
			for instructor_assignment in competition_doc.instructors:
				if instructor_assignment.instructor:
					# 验证指导教师是否存在
					if frappe.db.exists("Competition Regulation Competition Instructor", instructor_assignment.instructor):
						instructor_ids.append(instructor_assignment.instructor)

		if not instructor_ids:
			# 如果没有分配指导教师，返回空结果
			return []

		# 构建搜索条件
		conditions = [f"name IN ({','.join(['%s'] * len(instructor_ids))})"]
		values = instructor_ids + [start, page_len]

		if txt:
			conditions.append("(instructor_name LIKE %s OR college LIKE %s OR employee_id LIKE %s)")
			values = [f"%{txt}%", f"%{txt}%", f"%{txt}%"] + values

		where_clause = " AND ".join(conditions)

		# 查询指导教师信息
		query = f"""
			SELECT name, instructor_name, college, employee_id
			FROM `tabCompetition Regulation Competition Instructor`
			WHERE {where_clause}
			ORDER BY instructor_name
			LIMIT %s, %s
		"""

		return frappe.db.sql(query, values)

	except Exception as e:
		frappe.log_error(f"获取赛事指导教师失败: {e}")
		return []


@frappe.whitelist()
def debug_competition_instructors(competition):
	"""
	调试函数：检查赛事的指导教师数据
	"""
	try:
		# 检查赛事是否存在
		if not frappe.db.exists("Competition Regulation Competition", competition):
			return {"error": f"赛事不存在: {competition}"}

		# 获取赛事文档
		competition_doc = frappe.get_doc("Competition Regulation Competition", competition)

		result = {
			"competition": competition,
			"instructors_count": len(competition_doc.instructors) if competition_doc.instructors else 0,
			"instructors_data": [],
			"valid_instructors": [],
			"invalid_instructors": []
		}

		if competition_doc.instructors:
			for instructor_assignment in competition_doc.instructors:
				instructor_data = {
					"instructor_id": instructor_assignment.instructor
				}
				result["instructors_data"].append(instructor_data)

				# 检查指导教师是否存在
				if instructor_assignment.instructor:
					if frappe.db.exists("Competition Regulation Competition Instructor", instructor_assignment.instructor):
						instructor_doc = frappe.get_doc("Competition Regulation Competition Instructor", instructor_assignment.instructor)
						result["valid_instructors"].append({
							"id": instructor_doc.name,
							"name": instructor_doc.instructor_name,
							"college": instructor_doc.college,
							"employee_id": instructor_doc.employee_id
						})
					else:
						result["invalid_instructors"].append(instructor_assignment.instructor)

		return result

	except Exception as e:
		return {"error": str(e)}


@frappe.whitelist()
def test_instructor_query(competition):
	"""
	测试指导教师查询功能
	"""
	try:
		result = get_competition_instructors(
			"Competition Regulation Competition Instructor",
			"",
			"instructor_name",
			0,
			20,
			f'{{"competition": "{competition}"}}'
		)

		return {
			"competition": competition,
			"query_result": result,
			"result_count": len(result) if result else 0
		}

	except Exception as e:
		return {"error": str(e), "traceback": frappe.get_traceback()}
