{"actions": [], "creation": "2025-07-28 00:00:00.000000", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["student_id", "student_name", "college", "column_break_3", "invitation_status", "is_captain"], "fields": [{"fieldname": "student_id", "fieldtype": "Link", "in_list_view": 1, "label": "学号", "options": "Competition Regulation Student Info", "reqd": 1}, {"fetch_from": "student_id.student_name", "fieldname": "student_name", "fieldtype": "Data", "in_list_view": 1, "label": "姓名", "read_only": 1}, {"fetch_from": "student_id.college", "fieldname": "college", "fieldtype": "Data", "in_list_view": 1, "label": "学院/学部", "read_only": 1}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"default": "等待回应", "fieldname": "invitation_status", "fieldtype": "Select", "in_list_view": 1, "label": "邀请状态", "options": "等待回应\n已接受\n已拒绝\n已取消", "read_only": 1}, {"default": "0", "fieldname": "is_captain", "fieldtype": "Check", "label": "是否队长", "read_only": 1}], "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2025-07-28 00:00:00.000000", "modified_by": "Administrator", "module": "Competition Regulation", "name": "Competition Regulation Registration Participant", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}