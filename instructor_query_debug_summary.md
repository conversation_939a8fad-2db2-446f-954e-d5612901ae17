# 指导教师查询调试和修复总结

## 🎯 问题分析

用户反馈：指导教师筛选结果为空，需要检查函数和SQL语句是否与具体赛事指导教师名单的定义相符。

## 🔍 问题排查

### 1. 数据结构验证

#### A. 具体赛事DocType结构
```json
{
    "fieldname": "instructors",
    "fieldtype": "Table",
    "label": "指导教师名单",
    "options": "Competition Regulation Competition Instructor Assignment"
}
```

#### B. 指导教师分配子表结构
```json
{
    "fields": [
        {
            "fieldname": "instructor",
            "fieldtype": "Link",
            "options": "Competition Regulation Competition Instructor"
        },
        {
            "fieldname": "instructor_name",
            "fieldtype": "Data",
            "fetch_from": "instructor.instructor_name"
        },
        {
            "fieldname": "assignment_type",
            "fieldtype": "Select",
            "options": "手动分配\n自动分配"
        }
    ]
}
```

#### C. 指导教师主表结构
```json
{
    "fields": [
        {
            "fieldname": "instructor_name",
            "fieldtype": "Data"
        },
        {
            "fieldname": "employee_id",
            "fieldtype": "Data"
        },
        {
            "fieldname": "college",
            "fieldtype": "Data"
        }
    ]
}
```

**发现问题**：指导教师主表中没有`title`字段！

### 2. SQL查询问题

#### A. 修复前（错误）
```sql
SELECT name, instructor_name, title, college  -- ❌ title字段不存在
FROM `tabCompetition Regulation Competition Instructor`
WHERE name IN (...)
```

#### B. 修复后（正确）
```sql
SELECT name, instructor_name, college, employee_id  -- ✅ 使用实际存在的字段
FROM `tabCompetition Regulation Competition Instructor`
WHERE name IN (...)
```

### 3. 搜索条件问题

#### A. 修复前（错误）
```python
if txt:
    conditions.append("(instructor_name LIKE %s OR title LIKE %s OR college LIKE %s)")
    values = [f"%{txt}%", f"%{txt}%", f"%{txt}%"] + values
```

#### B. 修复后（正确）
```python
if txt:
    conditions.append("(instructor_name LIKE %s OR college LIKE %s)")
    values = [f"%{txt}%", f"%{txt}%"] + values
```

## 🔧 修复方案

### 1. 字段映射修正

| 原始查询字段 | 实际字段 | 状态 |
|-------------|----------|------|
| name | name | ✅ 正确 |
| instructor_name | instructor_name | ✅ 正确 |
| title | ❌ 不存在 | 🔧 移除 |
| college | college | ✅ 正确 |
| - | employee_id | 🆕 添加 |

### 2. 查询逻辑优化

```python
@frappe.whitelist()
def get_competition_instructors(doctype, txt, searchfield, start, page_len, filters):
    """
    获取赛事的指导教师列表
    """
    import json
    if isinstance(filters, str):
        filters = json.loads(filters)
    
    competition = filters.get('competition') if filters else None
    
    if not competition:
        return []
    
    try:
        # 验证赛事是否存在
        if not frappe.db.exists("Competition Regulation Competition", competition):
            return []
        
        # 获取赛事文档
        competition_doc = frappe.get_doc("Competition Regulation Competition", competition)
        
        # 从赛事的指导教师子表中获取指导教师ID列表
        instructor_ids = []
        if competition_doc.instructors:
            for instructor_assignment in competition_doc.instructors:
                if instructor_assignment.instructor:
                    if frappe.db.exists("Competition Regulation Competition Instructor", instructor_assignment.instructor):
                        instructor_ids.append(instructor_assignment.instructor)
        
        if not instructor_ids:
            return []
        
        # 构建搜索条件
        conditions = [f"name IN ({','.join(['%s'] * len(instructor_ids))})"]
        values = instructor_ids + [start, page_len]
        
        if txt:
            conditions.append("(instructor_name LIKE %s OR college LIKE %s)")
            values = [f"%{txt}%", f"%{txt}%"] + values
        
        where_clause = " AND ".join(conditions)
        
        # 查询指导教师信息
        query = f"""
            SELECT name, instructor_name, college, employee_id
            FROM `tabCompetition Regulation Competition Instructor`
            WHERE {where_clause}
            ORDER BY instructor_name
            LIMIT %s, %s
        """
        
        return frappe.db.sql(query, values)
        
    except Exception as e:
        frappe.log_error(f"获取赛事指导教师失败: {e}")
        return []
```

### 3. 调试功能添加

```python
@frappe.whitelist()
def debug_competition_instructors(competition):
    """
    调试函数：检查赛事的指导教师数据
    """
    try:
        competition_doc = frappe.get_doc("Competition Regulation Competition", competition)
        
        result = {
            "competition": competition,
            "instructors_count": len(competition_doc.instructors) if competition_doc.instructors else 0,
            "instructors_data": [],
            "valid_instructors": [],
            "invalid_instructors": []
        }
        
        if competition_doc.instructors:
            for instructor_assignment in competition_doc.instructors:
                # 收集子表数据
                instructor_data = {
                    "instructor_id": instructor_assignment.instructor,
                    "instructor_name": instructor_assignment.instructor_name,
                    "assignment_type": instructor_assignment.assignment_type
                }
                result["instructors_data"].append(instructor_data)
                
                # 验证指导教师主表数据
                if instructor_assignment.instructor:
                    if frappe.db.exists("Competition Regulation Competition Instructor", instructor_assignment.instructor):
                        instructor_doc = frappe.get_doc("Competition Regulation Competition Instructor", instructor_assignment.instructor)
                        result["valid_instructors"].append({
                            "id": instructor_doc.name,
                            "name": instructor_doc.instructor_name,
                            "college": instructor_doc.college,
                            "employee_id": instructor_doc.employee_id
                        })
                    else:
                        result["invalid_instructors"].append(instructor_assignment.instructor)
        
        return result
        
    except Exception as e:
        return {"error": str(e)}
```

## 🧪 调试步骤

### 1. 使用调试函数检查数据
```javascript
// 在浏览器控制台中执行
frappe.call({
    method: 'competition_regulation.competition_regulation.doctype.competition_regulation_registration.competition_regulation_registration.debug_competition_instructors',
    args: {
        competition: '你的赛事名称'
    },
    callback: function(r) {
        console.log('调试结果:', r.message);
    }
});
```

### 2. 检查数据完整性
- ✅ 赛事是否存在
- ✅ 赛事是否有指导教师子表数据
- ✅ 指导教师ID是否有效
- ✅ 指导教师主表记录是否存在

### 3. 验证查询结果
```python
# 直接测试查询函数
result = get_competition_instructors(
    "Competition Regulation Competition Instructor",
    "",
    "instructor_name", 
    0, 
    20, 
    '{"competition": "你的赛事名称"}'
)
print(f"查询结果: {result}")
```

## 📊 可能的问题原因

### 1. 数据问题
- ❌ 赛事没有分配指导教师
- ❌ 指导教师分配子表为空
- ❌ 指导教师ID无效或不存在

### 2. 字段问题
- ✅ 已修复：`title`字段不存在
- ✅ 已修复：搜索条件中的无效字段

### 3. 逻辑问题
- ✅ 已添加：完善的存在性检查
- ✅ 已添加：详细的调试日志

## ✅ 修复完成状态

- ✅ 修复了SQL查询中的无效字段`title`
- ✅ 更新了搜索条件，移除无效字段引用
- ✅ 添加了详细的调试日志
- ✅ 创建了专门的调试函数
- ✅ 增强了错误处理和数据验证

### 修复的文件
1. `competition_regulation_registration.py` - 修复SQL查询和添加调试功能

### 下一步建议
1. 使用`debug_competition_instructors`函数检查具体赛事的数据
2. 确认赛事是否正确分配了指导教师
3. 验证指导教师主表记录是否存在
4. 检查查询日志确认数据流

**现在查询函数与实际的数据结构完全匹配，应该能正确返回结果！**
