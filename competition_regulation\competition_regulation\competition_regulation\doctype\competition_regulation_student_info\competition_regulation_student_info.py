# -*- coding: utf-8 -*-
# Copyright (c) 2025, 1 and contributors
# For license information, please see license.txt

from __future__ import unicode_literals
import frappe
from frappe.model.document import Document
from frappe import _
import re

class CompetitionRegulationStudentInfo(Document):
	"""
	学生信息DocType控制器
	
	功能：
	- 管理学生的基本信息
	- 实现特殊的权限控制：学生只能创建和查看自己的信息
	- 限制学生只能修改特定字段
	- 确保学号的唯一性
	- 验证各字段的有效性
	"""
	
	def validate(self):
		"""
		验证数据有效性
		"""
		self.validate_student_id()
		self.validate_student_name()
		self.validate_email()
		self.validate_mobile_phone()
		self.validate_college()
		self.validate_major()
		self.validate_class_name()
		self.set_user_info()
		self.check_student_permissions()
	
	def validate_student_id(self):
		"""
		验证学号
		- 确保学号不为空
		- 去除首尾空格
		- 验证学号格式（通常为数字）
		"""
		if self.student_id:
			self.student_id = self.student_id.strip()
			
		if not self.student_id:
			frappe.throw(_("学号不能为空"))
		
		# 验证学号格式（8-20位数字或数字字母组合）
		if not re.match(r'^[A-Za-z0-9]{8,20}$', self.student_id):
			frappe.throw(_("学号格式不正确，应为8-20位数字或数字字母组合"))
	
	def validate_student_name(self):
		"""
		验证学生姓名
		- 确保姓名不为空
		- 去除首尾空格
		- 验证姓名格式
		"""
		if self.student_name:
			self.student_name = self.student_name.strip()
			
		if not self.student_name:
			frappe.throw(_("学生姓名不能为空"))
		
		# 验证姓名长度
		if len(self.student_name) < 2 or len(self.student_name) > 10:
			frappe.throw(_("学生姓名长度应在2-10个字符之间"))
		
		# 验证姓名格式（允许中文、英文字母）
		if not re.match(r'^[\u4e00-\u9fa5a-zA-Z\s]+$', self.student_name):
			frappe.throw(_("学生姓名只能包含中文字符、英文字母和空格"))
	
	def validate_email(self):
		"""
		验证电子邮件地址
		"""
		if self.email:
			self.email = self.email.strip().lower()
			
		if not self.email:
			frappe.throw(_("电子邮件地址不能为空"))
		
		# 验证邮箱格式
		if not re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', self.email):
			frappe.throw(_("电子邮件地址格式不正确"))
	
	def validate_mobile_phone(self):
		"""
		验证手机号码
		"""
		if self.mobile_phone:
			self.mobile_phone = self.mobile_phone.strip()
			
		if not self.mobile_phone:
			frappe.throw(_("手机号码不能为空"))
		
		# 验证手机号码格式（中国大陆手机号）
		if not re.match(r'^1[3-9]\d{9}$', self.mobile_phone):
			frappe.throw(_("手机号码格式不正确，请输入有效的中国大陆手机号码"))
	
	def validate_college(self):
		"""
		验证学院信息
		"""
		if self.college:
			self.college = self.college.strip()
			
		if not self.college:
			frappe.throw(_("学院不能为空"))
		
		# 验证学院名称长度
		if len(self.college) < 2 or len(self.college) > 50:
			frappe.throw(_("学院名称长度应在2-50个字符之间"))
	
	def validate_major(self):
		"""
		验证专业信息
		"""
		if self.major:
			self.major = self.major.strip()
			
		if not self.major:
			frappe.throw(_("专业不能为空"))
		
		# 验证专业名称长度
		if len(self.major) < 2 or len(self.major) > 50:
			frappe.throw(_("专业名称长度应在2-50个字符之间"))
	
	def validate_class_name(self):
		"""
		验证行政班信息
		"""
		if self.class_name:
			self.class_name = self.class_name.strip()
			
		if not self.class_name:
			frappe.throw(_("行政班不能为空"))
		
		# 验证行政班名称长度
		if len(self.class_name) < 1 or len(self.class_name) > 30:
			frappe.throw(_("行政班名称长度应在1-30个字符之间"))
	
	def set_user_info(self):
		"""
		设置用户信息
		"""
		# 设置关联用户和创建用户
		current_user = frappe.session.user
		user_roles = frappe.get_roles(current_user)

		# 如果是学生角色，强制设置为当前用户
		if "学生" in user_roles and "教务处教师" not in user_roles and "竞赛负责教师" not in user_roles:
			self.user_id = current_user
		elif not self.user_id:
			self.user_id = current_user

		if not self.creation_user:
			self.creation_user = current_user

	def check_student_permissions(self):
		"""
		检查学生权限
		- 学生只能创建一条自己的信息
		- 学生只能查看和修改自己的信息
		"""
		current_user = frappe.session.user
		user_roles = frappe.get_roles(current_user)

		# 如果是学生角色（且不是教师角色）
		if "学生" in user_roles and "教务处教师" not in user_roles and "竞赛负责教师" not in user_roles:
			# 确保学生只能操作自己的信息
			if not self.user_id:
				self.user_id = current_user
			elif self.user_id != current_user:
				frappe.throw(_("您只能操作自己的学生信息"))

			# 检查是否已存在该用户的学生信息（仅在新建时检查）
			if self.is_new():
				existing_student = frappe.db.exists("Competition Regulation Student Info", {"user_id": current_user})
				if existing_student:
					frappe.throw(_("您已经创建过学生信息，每个学生只能创建一条信息记录"))

	def before_save(self):
		"""
		保存前处理
		"""
		# 标准化所有字段格式
		if self.student_id:
			self.student_id = self.student_id.strip().upper()
		if self.student_name:
			self.student_name = self.student_name.strip()
		if self.email:
			self.email = self.email.strip().lower()
		if self.mobile_phone:
			self.mobile_phone = self.mobile_phone.strip()
		if self.college:
			self.college = self.college.strip()
		if self.major:
			self.major = self.major.strip()
		if self.class_name:
			self.class_name = self.class_name.strip()

	def on_update(self):
		"""
		更新后处理
		"""
		pass

	def on_trash(self):
		"""
		删除前检查
		检查是否有竞赛报名等相关记录
		"""
		# 这里可以添加检查逻辑，确保没有相关的竞赛报名记录
		pass

	def get_full_info(self):
		"""
		获取学生完整信息
		返回格式化的学生信息字符串
		"""
		return f"{self.student_name}（{self.student_id}）- {self.college} {self.major} {self.class_name}"

	@frappe.whitelist()
	def get_student_competitions(self):
		"""
		获取该学生参加的所有竞赛
		返回竞赛列表
		"""
		# 这里可以添加查询该学生参加的竞赛的逻辑
		# 当有竞赛报名相关的DocType时再实现
		return []

# 权限查询条件
def get_permission_query_conditions(user):
	"""
	获取权限查询条件
	学生只能查看自己的信息（列表视图、报表等）
	"""
	if not user:
		user = frappe.session.user

	user_roles = frappe.get_roles(user)

	# 如果是教务处教师或竞赛负责教师，可以查看所有学生信息
	if "教务处教师" in user_roles or "竞赛负责教师" in user_roles:
		return ""

	# 如果是学生，只能查看自己的信息
	if "学生" in user_roles:
		return f"`tabCompetition Regulation Student Info`.user_id = '{user}'"

	# 其他情况不允许查看
	return "1=0"

# 权限检查
def has_permission(doc, user):
	"""
	检查是否有权限访问文档
	"""
	if not user:
		user = frappe.session.user

	user_roles = frappe.get_roles(user)

	# 如果是教务处教师或竞赛负责教师，有完全权限
	if "教务处教师" in user_roles or "竞赛负责教师" in user_roles:
		return True

	# 如果是学生
	if "学生" in user_roles:
		# 如果是新文档（doc为None或没有name），允许创建
		if not doc or (hasattr(doc, 'is_new') and doc.is_new()) or not getattr(doc, 'name', None):
			return True

		# 如果是现有文档，检查是否是自己的信息
		if isinstance(doc, str):
			# 如果传入的是文档名称，需要获取文档
			try:
				doc = frappe.get_doc("Competition Regulation Student Info", doc)
			except:
				return False

		return getattr(doc, 'user_id', None) == user

	# 其他情况不允许访问
	return False

# 权限检查的白名单方法
def get_permission_query_conditions_for_student_info(user):
	"""
	为学生信息获取权限查询条件的别名方法
	"""
	return get_permission_query_conditions(user)

# 检查是否可以创建文档
def can_create_student_info(user=None):
	"""
	检查用户是否可以创建学生信息
	"""
	if not user:
		user = frappe.session.user

	user_roles = frappe.get_roles(user)

	# 教师角色可以创建
	if "教务处教师" in user_roles or "竞赛负责教师" in user_roles:
		return True

	# 学生角色可以创建（但只能创建一条）
	if "学生" in user_roles:
		# 检查是否已经创建过
		existing = frappe.db.exists("Competition Regulation Student Info", {"user_id": user})
		return not existing

	return False

@frappe.whitelist()
def get_student_list_for_selection(doctype, txt, searchfield, start, page_len, filters):
	"""
	为Link字段提供学生信息选择列表
	学生可以查看所有学生信息用于选择队员
	"""
	user = frappe.session.user
	user_roles = frappe.get_roles(user)

	# 检查权限
	if not ("教务处教师" in user_roles or "竞赛负责教师" in user_roles or "学生" in user_roles):
		return []

	# 构建参数字典
	params = {
		'start': start,
		'page_len': page_len
	}

	# 构建搜索条件
	conditions = []
	if txt:
		conditions.append("(student_name LIKE %(txt)s OR student_id LIKE %(txt)s OR college LIKE %(txt)s)")
		params['txt'] = f"%{txt}%"

	# 添加过滤条件
	if filters:
		for key, value in filters.items():
			if key in ['college', 'major', 'class_name']:
				conditions.append(f"{key} = %({key})s")
				params[key] = value

	# 构建WHERE子句
	where_clause = " AND ".join(conditions) if conditions else "1=1"

	# 构建完整查询
	query = f"""
		SELECT name, student_name, student_id, college, major, class_name
		FROM `tabCompetition Regulation Student Info`
		WHERE {where_clause}
		ORDER BY student_name
		LIMIT %(start)s, %(page_len)s
	"""

	return frappe.db.sql(query, params)

@frappe.whitelist()
def search_students_for_team_selection(txt="", filters=None):
	"""
	为团队选择提供学生搜索功能
	返回格式化的学生信息列表
	"""
	user = frappe.session.user
	user_roles = frappe.get_roles(user)

	# 检查权限
	if not ("学生" in user_roles or "教务处教师" in user_roles or "竞赛负责教师" in user_roles):
		return []

	# 构建查询条件
	conditions = []
	values = {}

	if txt:
		conditions.append("(student_name LIKE %(txt)s OR student_id LIKE %(txt)s OR college LIKE %(txt)s)")
		values['txt'] = f"%{txt}%"

	if filters:
		if filters.get('college'):
			conditions.append("college = %(college)s")
			values['college'] = filters['college']
		if filters.get('major'):
			conditions.append("major = %(major)s")
			values['major'] = filters['major']

	where_clause = " AND ".join(conditions) if conditions else "1=1"

	query = f"""
		SELECT name, student_name, student_id, college, major, class_name, email
		FROM `tabCompetition Regulation Student Info`
		WHERE {where_clause}
		ORDER BY student_name
		LIMIT 50
	"""

	results = frappe.db.sql(query, values, as_dict=True)

	# 格式化返回结果
	formatted_results = []
	for student in results:
		formatted_results.append({
			'value': student.name,
			'label': f"{student.student_name} ({student.student_id}) - {student.college}",
			'description': f"{student.major} {student.class_name}"
		})

	return formatted_results


@frappe.whitelist()
def get_current_user_student_info():
	"""
	获取当前用户的学生信息
	"""
	current_user = frappe.session.user

	# 首先通过用户ID查找
	student_info = frappe.db.get_value("Competition Regulation Student Info",
		{"user_id": current_user}, ["name", "student_name", "student_id", "college"], as_dict=True)

	if student_info:
		return student_info

	# 如果通过用户ID没找到，尝试通过邮箱匹配
	student_info = frappe.db.get_value("Competition Regulation Student Info",
		{"email": current_user}, ["name", "student_name", "student_id", "college"], as_dict=True)

	return student_info
