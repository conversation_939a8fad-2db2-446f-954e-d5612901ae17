# -*- coding: utf-8 -*-
# Copyright (c) 2025, 1 and contributors
# For license information, please see license.txt

from __future__ import unicode_literals
import frappe
from frappe.model.document import Document
import re

class CompetitionRegulationCompetitionInstructor(Document):
	"""
	竞赛指导教师信息DocType控制器
	
	功能：
	- 管理竞赛指导教师的基本信息
	- 验证教师信息的有效性
	- 确保职工号的唯一性
	- 提供教师信息的查询和管理功能
	"""

	def autoname(self):
		"""
		自动命名：使用姓名-职工号格式
		"""
		if self.instructor_name and self.employee_id:
			self.name = f"{self.instructor_name}-{self.employee_id}"

	def validate(self):
		"""
		验证数据有效性
		"""
		self.validate_instructor_name()
		self.validate_employee_id()
		self.validate_college()
	
	def validate_instructor_name(self):
		"""
		验证教师姓名
		- 确保姓名不为空
		- 去除首尾空格
		- 验证姓名格式（中文字符）
		"""
		if self.instructor_name:
			self.instructor_name = self.instructor_name.strip()
			
		if not self.instructor_name:
			frappe.throw("教师姓名不能为空")
		
		# 验证姓名长度
		if len(self.instructor_name) < 2 or len(self.instructor_name) > 10:
			frappe.throw("教师姓名长度应在2-10个字符之间")
		
		# 验证姓名格式（允许中文、英文字母）
		if not re.match(r'^[\u4e00-\u9fa5a-zA-Z\s]+$', self.instructor_name):
			frappe.throw("教师姓名只能包含中文字符、英文字母和空格")
	
	def validate_employee_id(self):
		"""
		验证职工号
		- 确保职工号不为空
		- 去除首尾空格
		- 验证职工号格式
		"""
		if self.employee_id:
			self.employee_id = self.employee_id.strip()
			
		if not self.employee_id:
			frappe.throw("职工号不能为空")
		
		# 验证职工号格式（数字和字母组合，6-20位）
		if not re.match(r'^[A-Za-z0-9]{6,20}$', self.employee_id):
			frappe.throw("职工号格式不正确，应为6-20位数字和字母组合")
	
	def validate_college(self):
		"""
		验证学院信息
		- 确保学院不为空
		- 去除首尾空格
		"""
		if self.college:
			self.college = self.college.strip()
			
		if not self.college:
			frappe.throw("学院不能为空")
		
		# 验证学院名称长度
		if len(self.college) < 2 or len(self.college) > 50:
			frappe.throw("学院名称长度应在2-50个字符之间")
	
	def before_save(self):
		"""
		保存前处理
		"""
		# 标准化所有字段格式
		if self.instructor_name:
			self.instructor_name = self.instructor_name.strip()
		if self.employee_id:
			self.employee_id = self.employee_id.strip().upper()  # 职工号统一转为大写
		if self.college:
			self.college = self.college.strip()
	
	def on_update(self):
		"""
		更新后处理
		"""
		pass
	
	def on_trash(self):
		"""
		删除前检查
		检查是否有竞赛正在使用此教师作为指导教师
		"""
		# 这里可以添加检查逻辑，确保没有竞赛正在使用此教师
		pass
	
	def get_full_info(self):
		"""
		获取教师完整信息
		返回格式化的教师信息字符串
		"""
		return f"{self.instructor_name}（{self.employee_id}）- {self.college}"
	
	@frappe.whitelist()
	def get_instructor_competitions(self):
		"""
		获取该教师指导的所有竞赛
		返回竞赛列表
		"""
		# 这里可以添加查询该教师指导的竞赛的逻辑
		# 当有竞赛相关的DocType时再实现
		return []

def has_permission(doc, user=None, permission_type="read"):
	"""
	自定义权限控制
	学生角色只能在Link字段中选择指导教师，不能访问列表页面
	"""
	if not user:
		user = frappe.session.user

	# 获取用户角色
	user_roles = frappe.get_roles(user)

	# 如果是学生角色
	if "学生" in user_roles:
		# 检查是否是通过Link字段访问（通过HTTP referrer判断）
		if frappe.request and hasattr(frappe.request, 'environ'):
			referrer = frappe.request.environ.get('HTTP_REFERER', '')

			# 如果是从表单页面访问（包含Form关键字），允许读取
			if 'Form' in referrer or 'form' in referrer:
				return permission_type == "read"

			# 如果是API调用（用于Link字段的搜索），允许读取
			if '/api/method/' in referrer or frappe.request.path.startswith('/api/'):
				return permission_type == "read"

			# 其他情况（如直接访问列表页面）不允许
			return False

	# 其他角色按默认权限处理
	return True
