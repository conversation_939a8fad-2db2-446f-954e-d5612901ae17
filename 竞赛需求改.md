## 竞赛系统

将高老师的需求直接喂给AI后，生成的APP有很多细节问题

1. 竞赛状态应随报名、比赛时间的推移自动变化，且报名结束时间不能晚于比赛开始时间（AI知道报名开始不晚于报名结束、比赛开始不晚于比赛结束）
2. 角色权限控制不够完整？
3. 队员直接引用学生就行，不用子表
4. 教师信息应建一个独立的doctype，然后与系统用户绑定；而非子表
5. 表单字段仍有多余项，没让生成的信息生成了一堆
6. 一共有9个DocType，测试脚本只生成了11个，太少



Frappe框架自带的功能：

- 通过Data Import导入信息，通过Data Export可按条件筛选、选择部分字段导出名单
- 邮件
- 用户管理



需求该有多详细？

考虑修改需求的结构，分DocType提出表单字段、命名规则、角色权限、由时间、用户操作触发的自动控制



- 教务处要求提交的总结信息包括：
  - 基本信息。包括：参赛队伍数量/人次，竞赛结果（获奖的 人/队 数量）
  - 获奖名单。包括：姓名、学号、学院、奖项、指导教师信息
  - 完整参赛未获奖名单。包括：姓名、学号、学院、指导教师信息
- 信息学部要求提供的汇总信息包括
  - 获奖名单统计。包括学部内学生组队的队伍数量和人数，学部外学生组队的队伍数量和人数，混合组队的队伍数量和人数
  - 各级比赛获奖数量汇总统计表

- 系统自动完成的功能

  - 比赛前的提醒电子邮件

  - 报名成功的通知电子邮件

  - 取消报名的通知电子邮件



## 需求改

#### 角色

- 教务处教师

- 竞赛负责教师

- 学生


#### 各DocType：

##### 赛事级别

只包含一项字段：名称

权限：学生、教务处教师可以查看，竞赛负责教师可以创建、编辑、查看、删除

##### 竞赛指导教师信息 

字段：姓名、职工号、学院

权限：学生不可见，教务处教师可以查看，竞赛负责教师可以创建、编辑、查看、删除

##### 学生信息

创建时必填：学号，姓名，性别，电子邮件地址，学院，专业，行政班，手机号码

权限：同一学生只能创建一条自己的信息，创建后学生只可查看自己的信息，只允许修改：学院，专业，行政班，手机号码；教务处教师、竞赛负责教师可以查看所有学生信息

##### 赛事

赛事名称、赛事级别、参赛形式（个人赛 / 团队赛，若为后者还要有人数限制）、报名起止日期时间、比赛起止日期时间、竞赛指导教师名单、是否自动分配指导教师、奖项设置、赛事介绍

如果一项比赛包含多个项目，需要按项目设定比赛属性。例如，本校机器人大赛内包括机器人仿真项目、计算机博弈项目

状态：报名未开始、报名中、报名结束、比赛中、比赛结束

系统自动实现

- 统计参赛队伍数量/人次、竞赛结果（获奖的 人/队 数量）
- 根据时间自动修改赛事状态

字段限制：报名开始时间 早于 报名截止时间 早于 比赛开始时间 早于 比赛截止时间

权限：教务处教师、学生可以查看，竞赛负责教师可以创建、编辑、查看、删除

##### 报名

字段：

赛事（只能选择“报名中”状态下的赛事）、比赛项目、团队名称、参赛人员名单（姓名、学号、学院、是否接受邀请，人数不得大于队伍最大人数约束）、指导教师、所获奖项、队员组成成分（信息学部内、信息学部外、混合）

状态：等待同意、成功报名、报名取消、获奖、未获奖、作弊取消成绩

功能：

报名创建者为队长，可邀请其他学生作为队员，队员可接受、拒绝

任何成员可取消报名，视为团队报名取消，队长可重新邀请其他队员

权限：同一学生只能在一项赛事中报名一次（“取消”的报名不算）、只能加入一个团队，只能查看自己所在队伍的记录，竞赛负责教师可以编辑、查看；成功报名后学生只能取消报名，不能修改其他任何字段；比赛开始后不能作任何修改

##### 全局设置

设置为单例

字段：

- 比赛前的提醒电子邮件模板

- 报名成功的通知电子邮件模板

- 取消报名的通知电子邮件模板

权限：学生不可见，竞赛负责教师可以编辑、查看



##### 赛事种类

字段：赛事名称、项目列表（子表，只包含项目名称）、赛事级别

权限：教务处教师、学生可以查看，竞赛负责教师可以创建、编辑、查看、删除

##### 具体赛事

赛事种类（从赛事名单中选择）、项目（从对应赛事的项目子表中选择）、参赛形式（个人赛 / 团队赛，若为后者还要有人数限制）、报名起止日期时间、比赛起止日期时间、竞赛指导教师名单、是否自动分配指导教师、奖项设置、赛事介绍

状态：报名未开始、报名中、报名结束、比赛中、比赛结束



系统自动实现

- 统计参赛队伍数量/人次、竞赛结果（获奖的 人/队 数量）
- 根据时间自动修改赛事状态

字段限制：报名开始时间 早于 报名截止时间 早于 比赛开始时间 早于 比赛截止时间

权限：教务处教师、学生可以查看，竞赛负责教师可以创建、编辑、查看、删除