# -*- coding: utf-8 -*-
# Copyright (c) 2025, 1 and contributors
# For license information, please see license.txt

from __future__ import unicode_literals
import frappe
from frappe.model.document import Document

class CompetitionRegulationRegistrationParticipant(Document):
	"""
	参赛人员子表DocType控制器
	
	功能：
	- 管理报名中的参赛人员信息
	- 处理邀请状态变更
	- 自动填充学生信息
	"""
	
	def validate(self):
		"""
		验证数据有效性
		"""
		self.validate_student_info()
		self.auto_fill_student_info()
	
	def validate_student_info(self):
		"""
		验证学生信息
		"""
		if not self.student_id:
			frappe.throw("请选择学生")
		
		# 验证学生信息是否存在
		if not frappe.db.exists("Competition Regulation Student Info", self.student_id):
			frappe.throw(f"学生信息 {self.student_id} 不存在")
	
	def auto_fill_student_info(self):
		"""
		自动填充学生信息
		"""
		if self.student_id:
			student_doc = frappe.get_doc("Competition Regulation Student Info", self.student_id)
			self.student_name = student_doc.student_name
			self.college = student_doc.college
	
	def before_save(self):
		"""
		保存前处理
		"""
		# 确保队长的邀请状态为"已接受"
		if self.is_captain and self.invitation_status != "已接受":
			self.invitation_status = "已接受"
