# -*- coding: utf-8 -*-
# Copyright (c) 2025, 1 and contributors
# For license information, please see license.txt

from __future__ import unicode_literals
import frappe
from frappe.model.document import Document
from frappe import _
from datetime import datetime

class CompetitionRegulationCompetition(Document):
	"""
	具体赛事DocType控制器
	
	功能：
	- 管理具体赛事的详细信息
	- 关联赛事种类和项目
	- 自动更新赛事状态
	- 管理指导教师分配
	- 统计参赛队伍和人员信息
	"""
	
	def autoname(self):
		"""
		自动命名
		"""
		if self.competition_title:
			self.competition_title = self.competition_title.strip()
			self.name = self.competition_title

	def validate(self):
		"""
		验证数据有效性
		"""
		self.validate_competition_title()
		self.validate_competition_category()
		self.validate_project_name()
		self.validate_participation_type()
		self.validate_datetime_sequence()
		self.validate_instructors()
		self.update_status()
		self.update_statistics()
	
	def validate_competition_title(self):
		"""
		验证赛事标题
		"""
		if self.competition_title:
			self.competition_title = self.competition_title.strip()
			
		if not self.competition_title:
			frappe.throw(_("赛事标题不能为空"))
		
		# 验证赛事标题长度
		if len(self.competition_title) < 2 or len(self.competition_title) > 100:
			frappe.throw(_("赛事标题长度应在2-100个字符之间"))
	
	def validate_competition_category(self):
		"""
		验证赛事种类
		"""
		if not self.competition_category:
			frappe.throw(_("赛事种类不能为空"))
		
		# 验证赛事种类是否存在
		if not frappe.db.exists("Competition Regulation Competition Category", self.competition_category):
			frappe.throw(_("赛事种类不存在：{0}").format(self.competition_category))
	
	def validate_project_name(self):
		"""
		验证项目名称
		"""
		if not self.project_name:
			frappe.throw(_("项目名称不能为空"))

		# 验证项目是否属于选定的赛事种类
		if self.competition_category:
			category_doc = frappe.get_doc("Competition Regulation Competition Category", self.competition_category)
			valid_projects = category_doc.get_projects_list()

			if self.project_name not in valid_projects:
				frappe.throw(_("项目 '{0}' 不属于赛事种类 '{1}'").format(self.project_name, self.competition_category))
	
	def validate_participation_type(self):
		"""
		验证参赛形式
		"""
		if not self.participation_type:
			frappe.throw(_("参赛形式不能为空"))
		
		# 如果是团队赛，必须设置团队人数限制
		if self.participation_type == "团队赛":
			if not self.team_size_limit or self.team_size_limit <= 0:
				frappe.throw(_("团队赛必须设置团队人数限制，且必须大于0"))
			
			if self.team_size_limit > 10:
				frappe.throw(_("团队人数限制不能超过10人"))
	
	def validate_datetime_sequence(self):
		"""
		验证时间序列
		"""
		if not all([self.registration_start_datetime, self.registration_end_datetime, 
				   self.competition_start_datetime, self.competition_end_datetime]):
			frappe.throw(_("所有时间字段都必须填写"))
		
		# 将字符串日期时间转换为datetime对象进行比较
		reg_start = frappe.utils.get_datetime(self.registration_start_datetime)
		reg_end = frappe.utils.get_datetime(self.registration_end_datetime)
		comp_start = frappe.utils.get_datetime(self.competition_start_datetime)
		comp_end = frappe.utils.get_datetime(self.competition_end_datetime)

		# 验证时间序列的合理性
		if reg_start >= reg_end:
			frappe.throw(_("报名开始时间必须早于报名结束时间"))

		if reg_end > comp_start:
			frappe.throw(_("报名结束时间不能晚于比赛开始时间"))

		if comp_start >= comp_end:
			frappe.throw(_("比赛开始时间必须早于比赛结束时间"))
	
	def validate_instructors(self):
		"""
		验证指导教师
		"""
		if self.instructors:
			instructor_list = []
			for instructor in self.instructors:
				if hasattr(instructor, 'validate'):
					instructor.validate()
				
				# 检查是否有重复的指导教师
				if instructor.instructor in instructor_list:
					frappe.throw(_("指导教师不能重复：{0}").format(instructor.instructor_name))
				instructor_list.append(instructor.instructor)
	
	def update_status(self):
		"""
		根据时间自动更新赛事状态
		"""
		if not all([self.registration_start_datetime, self.registration_end_datetime, 
				   self.competition_start_datetime, self.competition_end_datetime]):
			return
		
		now = frappe.utils.now_datetime()

		# 将字符串日期时间转换为datetime对象
		reg_start = frappe.utils.get_datetime(self.registration_start_datetime)
		reg_end = frappe.utils.get_datetime(self.registration_end_datetime)
		comp_start = frappe.utils.get_datetime(self.competition_start_datetime)
		comp_end = frappe.utils.get_datetime(self.competition_end_datetime)

		# 根据当前时间确定状态
		if now < reg_start:
			new_status = "报名未开始"
		elif now >= reg_start and now <= reg_end:
			new_status = "报名中"
		elif now > reg_end and now < comp_start:
			new_status = "报名结束"
		elif now >= comp_start and now <= comp_end:
			new_status = "比赛中"
		else:
			new_status = "比赛结束"
		
		# 只有状态发生变化时才更新
		if self.status != new_status:
			self.status = new_status
	
	def update_statistics(self):
		"""
		更新统计信息
		"""
		# 这里需要在有报名和获奖记录相关DocType后实现
		# 暂时设置为0
		self.total_teams = 0
		self.total_participants = 0
		self.awarded_teams = 0
		self.awarded_participants = 0
	
	def before_save(self):
		"""
		保存前处理
		"""
		# 标准化字段格式
		if self.competition_title:
			self.competition_title = self.competition_title.strip()
		
		if self.project_name:
			self.project_name = self.project_name.strip()

		# 处理指导教师子表数据清理
		if self.instructors:
			# 验证所有指导教师是否存在
			for instructor_assignment in self.instructors:
				if instructor_assignment.instructor and not frappe.db.exists("Competition Regulation Competition Instructor", instructor_assignment.instructor):
					frappe.throw(_("指导教师 {0} 不存在").format(instructor_assignment.instructor))
		
		# 如果启用自动分配指导教师，执行自动分配逻辑
		if self.auto_assign_instructor:
			self.auto_assign_instructors()
	
	def on_update(self):
		"""
		更新后处理
		"""
		# 可以在这里添加状态变化的通知逻辑
		pass
	
	def on_trash(self):
		"""
		删除前检查
		"""
		# 检查是否有报名记录引用此赛事
		existing_registrations = frappe.get_all("Competition Regulation Registration",
			filters={"competition": self.name})

		if existing_registrations:
			frappe.throw(_("无法删除赛事，还有{0}个报名记录正在使用此赛事").format(len(existing_registrations)))

	def get_awards_list(self):
		"""
		从奖项设置中解析奖项列表
		"""
		if not self.awards_setting:
			return []

		awards_list = []
		awards_text = self.awards_setting

		# 移除HTML标签
		import re
		awards_text = re.sub(r'<[^>]*>', '', awards_text)

		# 按行分割，解析奖项
		lines = awards_text.split('\n')
		for line in lines:
			award_name = line.strip()
			if award_name:
				# 检查是否包含奖项关键词
				if '奖' in award_name or 'Award' in award_name or 'Prize' in award_name:
					# 去除序号、符号等
					clean_award_name = re.sub(r'^[\d\.\-\*\s\u2022\u25cf]+', '', award_name).strip()
					# 去除末尾的冒号和数字描述
					clean_award_name = re.sub(r'[：:]\s*\d+.*$', '', clean_award_name).strip()

					if clean_award_name and clean_award_name not in awards_list:
						awards_list.append(clean_award_name)

		return awards_list
	
	@frappe.whitelist()
	def refresh_status(self):
		"""
		手动刷新状态
		"""
		self.update_status()
		self.update_statistics()
		self.save()
		frappe.msgprint(_("状态已更新"))
	
	def auto_assign_instructors(self):
		"""
		自动分配指导教师
		"""
		# 这里可以实现自动分配逻辑
		# 例如：根据教师的专业领域、工作负荷等因素自动分配
		pass

	@frappe.whitelist()
	def get_available_projects(self):
		"""
		获取当前赛事种类下的可用项目
		"""
		if not self.competition_category:
			return []

		category_doc = frappe.get_doc("Competition Regulation Competition Category", self.competition_category)
		return category_doc.get_projects_list()

	@frappe.whitelist()
	def get_competition_summary(self):
		"""
		获取赛事摘要信息
		"""
		return {
			"competition_title": self.competition_title,
			"competition_category": self.competition_category,
			"project_name": self.project_name,
			"participation_type": self.participation_type,
			"team_size_limit": self.team_size_limit if self.participation_type == "团队赛" else None,
			"status": self.status,
			"registration_period": {
				"start": self.registration_start_datetime,
				"end": self.registration_end_datetime
			},
			"competition_period": {
				"start": self.competition_start_datetime,
				"end": self.competition_end_datetime
			},
			"instructor_count": len(self.instructors) if self.instructors else 0,
			"statistics": {
				"total_teams": self.total_teams,
				"total_participants": self.total_participants,
				"awarded_teams": self.awarded_teams,
				"awarded_participants": self.awarded_participants
			}
		}

# 定时任务：自动更新赛事状态
def update_all_competition_status():
	"""
	定时任务函数：更新所有赛事的状态
	建议每小时执行一次
	"""
	competitions = frappe.get_all("Competition Regulation Competition",
		filters={"status": ["not in", ["比赛结束"]]},
		fields=["name"])

	updated_count = 0
	for comp in competitions:
		try:
			competition_doc = frappe.get_doc("Competition Regulation Competition", comp.name)
			old_status = competition_doc.status
			competition_doc.update_status()

			if old_status != competition_doc.status:
				competition_doc.save()
				updated_count += 1
				frappe.logger().info(f"Competition {comp.name} status updated from {old_status} to {competition_doc.status}")
		except Exception as e:
			frappe.logger().error(f"Error updating competition {comp.name}: {str(e)}")

	frappe.logger().info(f"Updated {updated_count} competition statuses")
	return updated_count
