# -*- coding: utf-8 -*-
from __future__ import unicode_literals
from . import __version__ as app_version

app_name = "competition_regulation"
app_title = "Competition Regulation"
app_publisher = "1"
app_description = "某大学本科生科技竞赛管理系统"
app_icon = "octicon octicon-trophy"
app_color = "blue"
app_email = "1@2.3"
app_license = "MIT"

# Includes in <head>
# ------------------

# include js, css files in header of desk.html
# app_include_css = "/assets/competition_regulation/css/competition_regulation.css"
# app_include_js = "/assets/competition_regulation/js/competition_regulation.js"

# include js, css files in header of web template
# web_include_css = "/assets/competition_regulation/css/competition_regulation.css"
# web_include_js = "/assets/competition_regulation/js/competition_regulation.js"

# include custom scss in every website theme (without file extension ".scss")
# website_theme_scss = "competition_regulation/public/scss/website"

# include js, css files in header of web form
# webform_include_js = {"doctype": "public/js/doctype.js"}
# webform_include_css = {"doctype": "public/css/doctype.css"}

# include js in page
# page_js = {"page" : "public/js/file.js"}

# include js in doctype views
# doctype_js = {"doctype" : "public/js/doctype.js"}
# doctype_list_js = {"doctype" : "public/js/doctype_list.js"}
# doctype_tree_js = {"doctype" : "public/js/doctype_tree.js"}
# doctype_calendar_js = {"doctype" : "public/js/doctype_calendar.js"}

# Home Pages
# ----------

# application home page (will override Website Settings)
# home_page = "login"

# website user home page (by Role)
# role_home_page = {
#	"Role": "home_page"
# }

# Generators
# ----------

# automatically create page for each record of this doctype
# website_generators = ["Web Page"]

# Installation
# ------------

# before_install = "competition_regulation.install.before_install"
# after_install = "competition_regulation.install.after_install"

# Desk Notifications
# ------------------
# See frappe.core.notifications.get_notification_config

# notification_config = "competition_regulation.notifications.get_notification_config"

# Permissions
# -----------
# Permissions evaluated in scripted ways

permission_query_conditions = {
	"Competition Regulation Student Info": "competition_regulation.competition_regulation.doctype.competition_regulation_student_info.competition_regulation_student_info.get_permission_query_conditions",
	"Competition Regulation Registration": "competition_regulation.competition_regulation.doctype.competition_regulation_registration.competition_regulation_registration.get_permission_query_conditions",
}

has_permission = {
	"Competition Regulation Student Info": "competition_regulation.competition_regulation.doctype.competition_regulation_student_info.competition_regulation_student_info.has_permission",
	"Competition Regulation Registration": "competition_regulation.competition_regulation.doctype.competition_regulation_registration.competition_regulation_registration.has_permission",
}

# DocType Class
# ---------------
# Override standard doctype classes

# override_doctype_class = {
# 	"ToDo": "custom_app.overrides.CustomToDo"
# }

# Document Events
# ---------------
# Hook on document methods and events

# doc_events = {
# 	"*": {
# 		"on_update": "method",
# 		"on_cancel": "method",
# 		"on_trash": "method"
#	}
# }

# Scheduled Tasks
# ---------------

scheduler_events = {
	"hourly": [
		"competition_regulation.tasks.update_competition_status",
		"competition_regulation.tasks.send_reminder_emails"
	]
}

# Testing
# -------

# before_tests = "competition_regulation.install.before_tests"

# Overriding Methods
# ------------------------------
#
# override_whitelisted_methods = {
# 	"frappe.desk.doctype.event.event.get_events": "competition_regulation.event.get_events"
# }
#
# each overriding function accepts a `data` argument;
# generated from the base implementation of the doctype dashboard,
# along with any modifications made in other Frappe apps
# override_doctype_dashboards = {
# 	"Task": "competition_regulation.task.get_dashboard_data"
# }

# exempt linked doctypes from being automatically cancelled
#
# auto_cancel_exempted_doctypes = ["Auto Repeat"]


# User Data Protection
# --------------------

user_data_fields = [
	{
		"doctype": "{doctype_1}",
		"filter_by": "{filter_by}",
		"redact_fields": ["{field_1}", "{field_2}"],
		"partial": 1,
	},
	{
		"doctype": "{doctype_2}",
		"filter_by": "{filter_by}",
		"partial": 1,
	},
	{
		"doctype": "{doctype_3}",
		"strict": False,
	},
	{
		"doctype": "{doctype_4}"
	}
]

# Authentication and authorization
# --------------------------------

# auth_hooks = [
# 	"competition_regulation.auth.validate"
# ]
