// Copyright (c) 2025, 1 and contributors
// For license information, please see license.txt

frappe.ui.form.on('Competition Regulation Competition Instructor', {
	// 表单刷新时触发
	refresh: function(frm) {
		// 设置字段描述
		frm.set_df_property('instructor_name', 'description', '请输入教师的真实姓名');
		frm.set_df_property('employee_id', 'description', '请输入6-20位数字和字母组合的职工号');
		frm.set_df_property('college', 'description', '请输入教师所属学院的完整名称');
		
		// 如果是新文档，设置焦点到姓名字段
		if (frm.is_new()) {
			frm.set_focus('instructor_name');
		}
		
		// 添加自定义按钮
		if (!frm.is_new()) {
			frm.add_custom_button('查看指导竞赛', function() {
				// 调用服务器端方法获取指导的竞赛
				frappe.call({
					method: 'get_instructor_competitions',
					doc: frm.doc,
					callback: function(r) {
						if (r.message && r.message.length > 0) {
							// 显示竞赛列表
							frappe.msgprint({
								title: '指导的竞赛',
								message: '该教师指导的竞赛：' + r.message.join(', ')
							});
						} else {
							frappe.msgprint('该教师暂未指导任何竞赛');
						}
					}
				});
			});
		}
	},
	
	// 教师姓名字段变化时触发
	instructor_name: function(frm) {
		// 自动去除首尾空格
		if (frm.doc.instructor_name) {
			frm.set_value('instructor_name', frm.doc.instructor_name.trim());
		}
	},
	
	// 职工号字段变化时触发
	employee_id: function(frm) {
		// 自动去除首尾空格并转为大写
		if (frm.doc.employee_id) {
			frm.set_value('employee_id', frm.doc.employee_id.trim().toUpperCase());
		}
	},
	
	// 学院字段变化时触发
	college: function(frm) {
		// 自动去除首尾空格
		if (frm.doc.college) {
			frm.set_value('college', frm.doc.college.trim());
		}
	},
	
	// 表单验证
	validate: function(frm) {
		// 验证教师姓名
		if (!frm.doc.instructor_name || frm.doc.instructor_name.trim() === '') {
			frappe.msgprint({
				title: '验证错误',
				indicator: 'red',
				message: '教师姓名不能为空'
			});
			frappe.validated = false;
			return false;
		}
		
		// 验证姓名长度
		if (frm.doc.instructor_name.length < 2 || frm.doc.instructor_name.length > 10) {
			frappe.msgprint({
				title: '验证错误',
				indicator: 'red',
				message: '教师姓名长度应在2-10个字符之间'
			});
			frappe.validated = false;
			return false;
		}
		
		// 验证职工号
		if (!frm.doc.employee_id || frm.doc.employee_id.trim() === '') {
			frappe.msgprint({
				title: '验证错误',
				indicator: 'red',
				message: '职工号不能为空'
			});
			frappe.validated = false;
			return false;
		}
		
		// 验证职工号格式
		var employeeIdPattern = /^[A-Za-z0-9]{6,20}$/;
		if (!employeeIdPattern.test(frm.doc.employee_id)) {
			frappe.msgprint({
				title: '验证错误',
				indicator: 'red',
				message: '职工号格式不正确，应为6-20位数字和字母组合'
			});
			frappe.validated = false;
			return false;
		}
		
		// 验证学院
		if (!frm.doc.college || frm.doc.college.trim() === '') {
			frappe.msgprint({
				title: '验证错误',
				indicator: 'red',
				message: '学院不能为空'
			});
			frappe.validated = false;
			return false;
		}
		
		// 验证学院名称长度
		if (frm.doc.college.length < 2 || frm.doc.college.length > 50) {
			frappe.msgprint({
				title: '验证错误',
				indicator: 'red',
				message: '学院名称长度应在2-50个字符之间'
			});
			frappe.validated = false;
			return false;
		}
	}
});
