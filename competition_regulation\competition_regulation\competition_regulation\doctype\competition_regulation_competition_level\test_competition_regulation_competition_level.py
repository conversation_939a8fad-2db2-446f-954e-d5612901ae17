# -*- coding: utf-8 -*-
# Copyright (c) 2025, 1 and Contributors
# See license.txt
from __future__ import unicode_literals

import frappe
import unittest

class TestCompetitionRegulationCompetitionLevel(unittest.TestCase):
	"""
	赛事级别DocType测试类
	
	测试内容：
	- 基本CRUD操作
	- 数据验证
	- 权限控制
	- 业务逻辑
	"""
	
	def setUp(self):
		"""
		测试前准备
		"""
		# 清理测试数据
		frappe.db.delete("Competition Regulation Competition Level", {"level_name": ["like", "测试%"]})
		frappe.db.commit()
	
	def tearDown(self):
		"""
		测试后清理
		"""
		# 清理测试数据
		frappe.db.delete("Competition Regulation Competition Level", {"level_name": ["like", "测试%"]})
		frappe.db.commit()
	
	def test_create_competition_level(self):
		"""
		测试创建赛事级别
		"""
		# 创建测试级别
		level = frappe.get_doc({
			"doctype": "Competition Regulation Competition Level",
			"level_name": "测试国家级"
		})
		level.insert()
		
		# 验证创建成功
		self.assertEqual(level.level_name, "测试国家级")
		self.assertTrue(level.name)
		
		# 验证数据库中存在
		saved_level = frappe.get_doc("Competition Regulation Competition Level", level.name)
		self.assertEqual(saved_level.level_name, "测试国家级")
	
	def test_level_name_validation(self):
		"""
		测试级别名称验证
		"""
		# 测试空名称
		with self.assertRaises(frappe.ValidationError):
			level = frappe.get_doc({
				"doctype": "Competition Regulation Competition Level",
				"level_name": ""
			})
			level.insert()
		
		# 测试只有空格的名称
		with self.assertRaises(frappe.ValidationError):
			level = frappe.get_doc({
				"doctype": "Competition Regulation Competition Level",
				"level_name": "   "
			})
			level.insert()
	
	def test_level_name_uniqueness(self):
		"""
		测试级别名称唯一性
		"""
		# 创建第一个级别
		level1 = frappe.get_doc({
			"doctype": "Competition Regulation Competition Level",
			"level_name": "测试省级"
		})
		level1.insert()
		
		# 尝试创建同名级别
		with self.assertRaises(frappe.DuplicateEntryError):
			level2 = frappe.get_doc({
				"doctype": "Competition Regulation Competition Level",
				"level_name": "测试省级"
			})
			level2.insert()
	
	def test_level_name_trimming(self):
		"""
		测试级别名称自动去除空格
		"""
		# 创建带空格的级别名称
		level = frappe.get_doc({
			"doctype": "Competition Regulation Competition Level",
			"level_name": "  测试市级  "
		})
		level.insert()
		
		# 验证空格被自动去除
		self.assertEqual(level.level_name, "测试市级")
	
	def test_update_competition_level(self):
		"""
		测试更新赛事级别
		"""
		# 创建级别
		level = frappe.get_doc({
			"doctype": "Competition Regulation Competition Level",
			"level_name": "测试校级"
		})
		level.insert()
		
		# 更新级别名称
		level.level_name = "测试院级"
		level.save()
		
		# 验证更新成功
		updated_level = frappe.get_doc("Competition Regulation Competition Level", level.name)
		self.assertEqual(updated_level.level_name, "测试院级")
	
	def test_delete_competition_level(self):
		"""
		测试删除赛事级别
		"""
		# 创建级别
		level = frappe.get_doc({
			"doctype": "Competition Regulation Competition Level",
			"level_name": "测试删除级别"
		})
		level.insert()
		level_name = level.name
		
		# 删除级别
		level.delete()
		
		# 验证删除成功
		self.assertFalse(frappe.db.exists("Competition Regulation Competition Level", level_name))
	
	def test_permissions(self):
		"""
		测试权限设置
		"""
		# 这里可以添加权限相关的测试
		# 由于权限测试需要特定的用户角色设置，暂时跳过
		pass
	
	def test_autoname_functionality(self):
		"""
		测试自动命名功能
		"""
		# 创建级别
		level = frappe.get_doc({
			"doctype": "Competition Regulation Competition Level",
			"level_name": "测试自动命名"
		})
		level.insert()
		
		# 验证文档名称与级别名称一致
		self.assertEqual(level.name, "测试自动命名")
