# -*- coding: utf-8 -*-
# Copyright (c) 2025, 1 and contributors
# For license information, please see license.txt

from __future__ import unicode_literals
import frappe
from frappe.model.document import Document
from frappe import _
import re

class CompetitionRegulationSettings(Document):
	"""
	全局设置DocType控制器（单例模式）
	
	功能：
	- 管理系统全局配置
	- 邮件模板管理
	- 模板变量验证
	- 提供邮件发送接口
	"""
	
	def validate(self):
		"""
		验证数据有效性
		"""
		self.validate_email_templates()
	
	def validate_email_templates(self):
		"""
		验证邮件模板
		"""
		templates = [
			("competition_reminder_email_template", "比赛前提醒邮件模板"),
			("registration_success_email_template", "报名成功通知邮件模板"),
			("registration_cancel_email_template", "取消报名通知邮件模板")
		]
		
		for field_name, field_label in templates:
			template_content = getattr(self, field_name)
			if template_content:
				self.validate_template_syntax(template_content, field_label)
	
	def validate_template_syntax(self, template_content, template_name):
		"""
		验证模板语法
		"""
		# 检查模板变量语法
		invalid_variables = []
		
		# 查找所有 {{ variable }} 格式的变量
		variables = re.findall(r'\{\{\s*(\w+)\s*\}\}', template_content)
		
		# 定义允许的变量
		allowed_variables = [
			'student_name', 'student_id', 'team_name', 'competition_name',
			'competition_project', 'competition_level', 'registration_datetime',
			'competition_start_datetime', 'competition_end_datetime',
			'instructor_name', 'college'
		]
		
		for var in variables:
			if var not in allowed_variables:
				invalid_variables.append(var)
		
		if invalid_variables:
			frappe.throw(_("{0}中包含无效的模板变量：{1}").format(
				template_name, ", ".join(invalid_variables)))
	
	def before_save(self):
		"""
		保存前处理
		"""
		# 设置默认模板（如果为空）
		self.set_default_templates()
	
	def set_default_templates(self):
		"""
		设置默认邮件模板
		"""
		if not self.competition_reminder_email_template:
			self.competition_reminder_email_template = self.get_default_reminder_template()
		
		if not self.registration_success_email_template:
			self.registration_success_email_template = self.get_default_success_template()
		
		if not self.registration_cancel_email_template:
			self.registration_cancel_email_template = self.get_default_cancel_template()
	
	def get_default_reminder_template(self):
		"""
		获取默认比赛提醒邮件模板
		"""
		return """
<p>亲爱的 {{ student_name }} 同学，您好！</p>

<p>这是一封比赛提醒邮件。</p>

<p><strong>比赛信息：</strong></p>
<ul>
<li>赛事名称：{{ competition_name }}</li>
<li>比赛项目：{{ competition_project }}</li>
<li>赛事级别：{{ competition_level }}</li>
<li>团队名称：{{ team_name }}</li>
<li>比赛时间：{{ competition_start_datetime }} 至 {{ competition_end_datetime }}</li>
</ul>

<p><strong>参赛信息：</strong></p>
<ul>
<li>学号：{{ student_id }}</li>
<li>学院：{{ college }}</li>
<li>指导教师：{{ instructor_name }}</li>
</ul>

<p>请您做好充分准备，按时参加比赛。祝您取得优异成绩！</p>

<p>如有疑问，请联系竞赛负责教师。</p>

<p>此致<br>
敬礼！</p>

<p>竞赛管理系统<br>
{{ registration_datetime }}</p>
		""".strip()
	
	def get_default_success_template(self):
		"""
		获取默认报名成功邮件模板
		"""
		return """
<p>亲爱的 {{ student_name }} 同学，您好！</p>

<p>恭喜您！您的团队已成功报名参加竞赛。</p>

<p><strong>报名信息：</strong></p>
<ul>
<li>赛事名称：{{ competition_name }}</li>
<li>比赛项目：{{ competition_project }}</li>
<li>赛事级别：{{ competition_level }}</li>
<li>团队名称：{{ team_name }}</li>
<li>报名时间：{{ registration_datetime }}</li>
</ul>

<p><strong>比赛安排：</strong></p>
<ul>
<li>比赛开始时间：{{ competition_start_datetime }}</li>
<li>比赛结束时间：{{ competition_end_datetime }}</li>
</ul>

<p><strong>您的信息：</strong></p>
<ul>
<li>学号：{{ student_id }}</li>
<li>学院：{{ college }}</li>
<li>指导教师：{{ instructor_name }}</li>
</ul>

<p>请您关注比赛相关通知，做好充分准备。</p>

<p>祝您在比赛中取得优异成绩！</p>

<p>此致<br>
敬礼！</p>

<p>竞赛管理系统<br>
{{ registration_datetime }}</p>
		""".strip()
	
	def get_default_cancel_template(self):
		"""
		获取默认取消报名邮件模板
		"""
		return """
<p>亲爱的 {{ student_name }} 同学，您好！</p>

<p>您的团队已取消报名参加以下竞赛：</p>

<p><strong>取消的报名信息：</strong></p>
<ul>
<li>赛事名称：{{ competition_name }}</li>
<li>比赛项目：{{ competition_project }}</li>
<li>团队名称：{{ team_name }}</li>
<li>原报名时间：{{ registration_datetime }}</li>
</ul>

<p><strong>您的信息：</strong></p>
<ul>
<li>学号：{{ student_id }}</li>
<li>学院：{{ college }}</li>
</ul>

<p>如果您需要重新报名，请在报名截止时间前重新提交报名申请。</p>

<p>如有疑问，请联系竞赛负责教师。</p>

<p>此致<br>
敬礼！</p>

<p>竞赛管理系统</p>
		""".strip()
	
	@frappe.whitelist()
	def preview_template(self, template_type):
		"""
		预览邮件模板
		"""
		template_field_map = {
			"reminder": "competition_reminder_email_template",
			"success": "registration_success_email_template",
			"cancel": "registration_cancel_email_template"
		}
		
		if template_type not in template_field_map:
			frappe.throw(_("无效的模板类型"))
		
		template_content = getattr(self, template_field_map[template_type])
		
		# 使用示例数据渲染模板
		sample_data = {
			"student_name": "张三",
			"student_id": "2021001001",
			"team_name": "示例团队",
			"competition_name": "示例竞赛",
			"competition_project": "示例项目",
			"competition_level": "校级",
			"registration_datetime": "2025-07-22 10:00:00",
			"competition_start_datetime": "2025-08-01 09:00:00",
			"competition_end_datetime": "2025-08-01 17:00:00",
			"instructor_name": "李教师",
			"college": "计算机科学与技术学院"
		}
		
		return self.render_template(template_content, sample_data)
	
	def render_template(self, template_content, data):
		"""
		渲染邮件模板
		"""
		if not template_content:
			return ""
		
		rendered_content = template_content
		
		# 替换模板变量
		for key, value in data.items():
			placeholder = "{{ " + key + " }}"
			rendered_content = rendered_content.replace(placeholder, str(value or ""))
		
		return rendered_content
	
	@staticmethod
	def get_settings():
		"""
		获取全局设置实例
		"""
		settings = frappe.get_single("Competition Regulation Settings")
		return settings
	
	@staticmethod
	def send_competition_reminder_email(registration_doc, member_doc):
		"""
		发送比赛提醒邮件
		"""
		settings = CompetitionRegulationSettings.get_settings()
		if not settings.competition_reminder_email_template:
			return False
		
		data = CompetitionRegulationSettings.prepare_email_data(registration_doc, member_doc)
		content = settings.render_template(settings.competition_reminder_email_template, data)
		
		return CompetitionRegulationSettings.send_email(
			recipient=data.get("student_email"),
			subject=f"比赛提醒 - {data.get('competition_name')}",
			content=content
		)
	
	@staticmethod
	def send_registration_success_email(registration_doc, member_doc):
		"""
		发送报名成功邮件
		"""
		settings = CompetitionRegulationSettings.get_settings()
		if not settings.registration_success_email_template:
			return False
		
		data = CompetitionRegulationSettings.prepare_email_data(registration_doc, member_doc)
		content = settings.render_template(settings.registration_success_email_template, data)
		
		return CompetitionRegulationSettings.send_email(
			recipient=data.get("student_email"),
			subject=f"报名成功通知 - {data.get('competition_name')}",
			content=content
		)
	
	@staticmethod
	def send_registration_cancel_email(registration_doc, member_doc):
		"""
		发送取消报名邮件
		"""
		settings = CompetitionRegulationSettings.get_settings()
		if not settings.registration_cancel_email_template:
			return False
		
		data = CompetitionRegulationSettings.prepare_email_data(registration_doc, member_doc)
		content = settings.render_template(settings.registration_cancel_email_template, data)
		
		return CompetitionRegulationSettings.send_email(
			recipient=data.get("student_email"),
			subject=f"取消报名通知 - {data.get('competition_name')}",
			content=content
		)
	
	@staticmethod
	def prepare_email_data(registration_doc, member_doc):
		"""
		准备邮件数据
		"""
		# 获取学生信息
		student_doc = frappe.get_doc("Competition Regulation Student Info", member_doc.student_info)
		
		# 获取赛事信息
		competition_doc = frappe.get_doc("Competition Regulation Competition", registration_doc.competition)
		
		# 获取项目信息
		project = None
		for proj in competition_doc.projects:
			if proj.project_name == registration_doc.competition_project:
				project = proj
				break
		
		# 获取指导教师信息
		instructor_name = ""
		if registration_doc.instructor:
			instructor_doc = frappe.get_doc("Competition Regulation Competition Instructor", registration_doc.instructor)
			instructor_name = instructor_doc.instructor_name
		
		return {
			"student_name": student_doc.student_name,
			"student_id": student_doc.student_id,
			"student_email": student_doc.email,
			"team_name": registration_doc.team_name,
			"competition_name": competition_doc.competition_name,
			"competition_project": registration_doc.competition_project,
			"competition_level": competition_doc.competition_level,
			"registration_datetime": frappe.utils.format_datetime(registration_doc.registration_datetime),
			"competition_start_datetime": frappe.utils.format_datetime(project.competition_start_datetime) if project else "",
			"competition_end_datetime": frappe.utils.format_datetime(project.competition_end_datetime) if project else "",
			"instructor_name": instructor_name,
			"college": student_doc.college
		}
	
	@staticmethod
	def send_email(recipient, subject, content):
		"""
		发送邮件
		"""
		try:
			frappe.sendmail(
				recipients=[recipient],
				subject=subject,
				message=content,
				as_markdown=False
			)
			return True
		except Exception as e:
			frappe.logger().error(f"Failed to send email to {recipient}: {str(e)}")
			return False
